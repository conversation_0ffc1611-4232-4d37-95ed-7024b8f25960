import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@angular/core";
import { Observable, Subscription } from "rxjs";
import {
  AdvancedNotificationService,
  AdvancedNotification,
} from "../../../services/advanced-notification.service";

@Component({
  selector: "app-notification-display",
  templateUrl: "./notification-display.component.html",
  styleUrls: ["./notification-display.component.css"],
})
export class NotificationDisplayComponent implements OnInit, OnDestroy {
  notifications$: Observable<AdvancedNotification[]>;
  private subscription?: Subscription;

  constructor(private notificationService: AdvancedNotificationService) {
    this.notifications$ = this.notificationService.getNotifications();
  }

  ngOnInit(): void {
    // Demander la permission pour les notifications navigateur
    this.requestNotificationPermission();
  }

  ngOnDestroy(): void {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

  private async requestNotificationPermission(): Promise<void> {
    if ("Notification" in window && Notification.permission === "default") {
      await Notification.requestPermission();
    }
  }

  onNotificationClick(notification: AdvancedNotification): void {
    this.notificationService.click(notification.id);

    // Exécuter les actions si disponibles
    if (notification.actions && notification.actions.length > 0) {
      // Par défaut, exécuter la première action
      notification.actions[0].action();
    }
  }

  onActionClick(
    notification: AdvancedNotification,
    actionId: string,
    event: Event
  ): void {
    event.stopPropagation();

    const action = notification.actions?.find((a) => a.id === actionId);
    if (action) {
      action.action();
    }
  }

  onDismiss(notification: AdvancedNotification, event: Event): void {
    event.stopPropagation();
    this.notificationService.dismiss(notification.id);
  }

  getNotificationClasses(notification: AdvancedNotification): string {
    const classes = ["notification", notification.type];

    if (notification.animation) {
      classes.push(`animation-${notification.animation}`);
    }

    if (
      notification.priority === "high" ||
      notification.priority === "urgent"
    ) {
      classes.push("high-priority");
    }

    classes.push("show");

    return classes.join(" ");
  }

  getPositionClasses(notification: AdvancedNotification): string {
    return `position-${notification.position || "top-right"}`;
  }

  formatTimestamp(timestamp: number): string {
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now.getTime() - date.getTime();

    if (diff < 60000) {
      // moins d'une minute
      return "À l'instant";
    } else if (diff < 3600000) {
      // moins d'une heure
      const minutes = Math.floor(diff / 60000);
      return `Il y a ${minutes} min`;
    } else if (diff < 86400000) {
      // moins d'un jour
      const hours = Math.floor(diff / 3600000);
      return `Il y a ${hours}h`;
    } else {
      return date.toLocaleDateString();
    }
  }

  getProgressWidth(notification: AdvancedNotification): number {
    if (!notification.progress) return 0;
    return Math.min(100, Math.max(0, notification.progress));
  }

  trackByNotificationId(
    index: number,
    notification: AdvancedNotification
  ): string {
    return notification.id;
  }

  // Méthodes de test pour le développement
  testNotification(type: "success" | "error" | "warning" | "info"): void {
    const messages = {
      success: { title: "Succès", message: "Opération réussie avec succès!" },
      error: {
        title: "Erreur",
        message: "Une erreur est survenue lors de l'opération.",
      },
      warning: {
        title: "Attention",
        message: "Attention, cette action peut avoir des conséquences.",
      },
      info: {
        title: "Information",
        message: "Voici une information importante.",
      },
    };

    this.notificationService.show({
      type,
      title: messages[type].title,
      message: messages[type].message,
      duration: 5000,
    });
  }

  testTemplate(): void {
    this.notificationService.showFromTemplate("message_received", {
      sender: "John Doe",
      message: "Salut! Comment ça va?",
    });
  }
}
