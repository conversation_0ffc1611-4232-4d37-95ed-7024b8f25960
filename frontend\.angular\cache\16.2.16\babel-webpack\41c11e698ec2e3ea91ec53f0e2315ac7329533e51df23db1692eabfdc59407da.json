{"ast": null, "code": "import { CommonModule, DatePipe } from '@angular/common';\nimport { PlanningsRoutingModule } from './plannings-routing.module';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { PipesModule } from '../../../pipes/pipes.module';\nimport { CalendarModule } from 'angular-calendar';\nimport * as i0 from \"@angular/core\";\nexport let PlanningsModule = /*#__PURE__*/(() => {\n  class PlanningsModule {\n    static {\n      this.ɵfac = function PlanningsModule_Factory(t) {\n        return new (t || PlanningsModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: PlanningsModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        providers: [DatePipe],\n        imports: [CommonModule, PlanningsRoutingModule, FormsModule, ReactiveFormsModule, CalendarModule, PipesModule]\n      });\n    }\n  }\n  return PlanningsModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}