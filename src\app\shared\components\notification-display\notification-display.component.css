/* ============================================================================
   NOTIFICATION DISPLAY COMPONENT
   ============================================================================ */

.notification-container {
  position: fixed;
  z-index: 9999;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  max-width: 28rem;
  pointer-events: none;
  padding: 1rem;
}

/* Positions */
.position-top-right {
  top: 0;
  right: 0;
}

.position-top-left {
  top: 0;
  left: 0;
}

.position-bottom-right {
  bottom: 0;
  right: 0;
}

.position-bottom-left {
  bottom: 0;
  left: 0;
}

.position-center {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  max-width: 32rem;
}

/* Notification de base */
.notification {
  background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
  border: 1px solid #374151;
  border-radius: 0.75rem;
  padding: 1.25rem;
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.05);
  pointer-events: auto;
  transform: translateX(100%);
  opacity: 0;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  cursor: pointer;
  backdrop-filter: blur(10px);
}

.notification.show {
  transform: translateX(0);
  opacity: 1;
}

.notification:hover {
  transform: translateX(-4px);
  box-shadow: 
    0 25px 50px rgba(0, 0, 0, 0.5),
    0 0 0 1px rgba(255, 255, 255, 0.1);
}

/* Types de notifications avec effets futuristes */
.notification.success {
  border-left: 4px solid #10b981;
  background: linear-gradient(135deg, #065f46 0%, #047857 100%);
  box-shadow: 
    0 20px 40px rgba(16, 185, 129, 0.2),
    0 0 20px rgba(16, 185, 129, 0.1);
}

.notification.error {
  border-left: 4px solid #ef4444;
  background: linear-gradient(135deg, #7f1d1d 0%, #991b1b 100%);
  box-shadow: 
    0 20px 40px rgba(239, 68, 68, 0.2),
    0 0 20px rgba(239, 68, 68, 0.1);
}

.notification.warning {
  border-left: 4px solid #f59e0b;
  background: linear-gradient(135deg, #78350f 0%, #92400e 100%);
  box-shadow: 
    0 20px 40px rgba(245, 158, 11, 0.2),
    0 0 20px rgba(245, 158, 11, 0.1);
}

.notification.info {
  border-left: 4px solid #3b82f6;
  background: linear-gradient(135deg, #1e3a8a 0%, #1d4ed8 100%);
  box-shadow: 
    0 20px 40px rgba(59, 130, 246, 0.2),
    0 0 20px rgba(59, 130, 246, 0.1);
}

/* En-tête */
.notification-header {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
}

.notification-icon {
  flex-shrink: 0;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  font-size: 1.125rem;
  color: #ffffff;
}

.notification-title-section {
  flex: 1;
  min-width: 0;
}

.notification-title {
  font-size: 1rem;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 0.25rem 0;
  line-height: 1.4;
}

.notification-timestamp {
  font-size: 0.75rem;
  color: #9ca3af;
  opacity: 0.8;
}

.notification-close {
  flex-shrink: 0;
  width: 1.75rem;
  height: 1.75rem;
  border: none;
  background: rgba(255, 255, 255, 0.1);
  color: #9ca3af;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.75rem;
}

.notification-close:hover {
  background: rgba(255, 255, 255, 0.2);
  color: #ffffff;
  transform: scale(1.1);
}

/* Contenu */
.notification-content {
  margin-bottom: 1rem;
}

.notification-message {
  font-size: 0.875rem;
  color: #e5e7eb;
  line-height: 1.5;
  margin: 0 0 0.75rem 0;
}

.notification-image {
  margin-top: 0.75rem;
  border-radius: 0.5rem;
  overflow: hidden;
}

.notification-image img {
  width: 100%;
  height: auto;
  display: block;
}

/* Barre de progression */
.notification-progress {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-top: 0.75rem;
}

.progress-bar {
  flex: 1;
  height: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 0.25rem;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #06b6d4);
  border-radius: 0.25rem;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 0.75rem;
  color: #9ca3af;
  font-weight: 500;
}

/* Actions */
.notification-actions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.5rem 0.75rem;
  border: 1px solid transparent;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
}

.action-primary {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: #ffffff;
  border-color: #3b82f6;
}

.action-primary:hover {
  background: linear-gradient(135deg, #2563eb, #1e40af);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.action-secondary {
  background: rgba(255, 255, 255, 0.1);
  color: #e5e7eb;
  border-color: rgba(255, 255, 255, 0.2);
}

.action-secondary:hover {
  background: rgba(255, 255, 255, 0.2);
  color: #ffffff;
}

.action-danger {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: #ffffff;
  border-color: #ef4444;
}

.action-danger:hover {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

/* Indicateur de priorité */
.priority-indicator {
  position: absolute;
  top: 0;
  right: 0;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 0 1rem 1rem 0;
  border-color: transparent #f59e0b transparent transparent;
}

.priority-indicator.urgent {
  border-color: transparent #ef4444 transparent transparent;
  animation: urgentPulse 1s ease-in-out infinite alternate;
}

/* Badge */
.notification-badge {
  position: absolute;
  top: -0.5rem;
  right: -0.5rem;
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: #ffffff;
  font-size: 0.625rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 1rem;
  min-width: 1.25rem;
  text-align: center;
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
}

/* Animations */
@keyframes urgentPulse {
  0% { opacity: 0.7; }
  100% { opacity: 1; }
}

.animation-slide {
  animation: slideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.animation-fade {
  animation: fadeIn 0.4s ease;
}

.animation-bounce {
  animation: bounceIn 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.animation-scale {
  animation: scaleIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideIn {
  from { transform: translateX(100%); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes bounceIn {
  0% { transform: scale(0.3); opacity: 0; }
  50% { transform: scale(1.05); }
  70% { transform: scale(0.9); }
  100% { transform: scale(1); opacity: 1; }
}

@keyframes scaleIn {
  from { transform: scale(0.8); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

/* Priorité haute */
.notification.high-priority {
  border-width: 2px;
  animation: highPriorityGlow 2s ease-in-out infinite alternate;
}

@keyframes highPriorityGlow {
  0% { box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4), 0 0 20px rgba(59, 130, 246, 0.2); }
  100% { box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4), 0 0 30px rgba(59, 130, 246, 0.4); }
}

/* Contrôles de test (développement) */
.notification-test-controls {
  position: fixed;
  bottom: 1rem;
  left: 1rem;
  display: flex;
  gap: 0.5rem;
  z-index: 10000;
}

.test-btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.test-btn.success { background: #10b981; color: white; }
.test-btn.error { background: #ef4444; color: white; }
.test-btn.warning { background: #f59e0b; color: white; }
.test-btn.info { background: #3b82f6; color: white; }
.test-btn.template { background: #8b5cf6; color: white; }

.test-btn:hover {
  transform: translateY(-1px);
  opacity: 0.9;
}

/* Responsive */
@media (max-width: 768px) {
  .notification-container {
    max-width: calc(100vw - 2rem);
    padding: 0.5rem;
  }
  
  .notification {
    padding: 1rem;
  }
  
  .notification-title {
    font-size: 0.875rem;
  }
  
  .notification-message {
    font-size: 0.8rem;
  }
  
  .action-btn {
    padding: 0.375rem 0.625rem;
    font-size: 0.7rem;
  }
}
