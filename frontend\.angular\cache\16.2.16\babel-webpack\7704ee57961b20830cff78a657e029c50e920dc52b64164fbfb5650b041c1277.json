{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/OneDrive/Bureau/Project PI/devBridge/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { BehaviorSubject, of } from 'rxjs';\nimport { distinctUntilChanged, switchMap, catchError, tap, filter } from 'rxjs/operators';\nimport { MessageType, CallType } from '../../../../models/message.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../services/message.service\";\nimport * as i2 from \"../../../../services/auth.service\";\nimport * as i3 from \"../../../../services/toast.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/forms\";\nimport * as i7 from \"../../../../components/system-status/system-status.component\";\nconst _c0 = [\"messagesContainer\"];\nconst _c1 = [\"messageInput\"];\nconst _c2 = [\"fileInput\"];\nconst _c3 = [\"voiceRecorder\"];\nfunction MessageChatComponent_div_0_span_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.selectedConversation.participants == null ? null : ctx_r2.selectedConversation.participants.length, \" participants \");\n  }\n}\nfunction MessageChatComponent_div_0_span_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"En ligne\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageChatComponent_div_0_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Hors ligne\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageChatComponent_div_0_button_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_0_button_12_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r20 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r20.startAudioCall());\n    });\n    i0.ɵɵelement(1, \"i\", 38);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageChatComponent_div_0_button_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_0_button_13_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r22 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r22.startVideoCall());\n    });\n    i0.ɵɵelement(1, \"i\", 40);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageChatComponent_div_0_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41);\n    i0.ɵɵelement(1, \"div\", 42);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageChatComponent_div_0_div_19_img_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 66);\n  }\n  if (rf & 2) {\n    const message_r24 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"src\", message_r24.sender.image || \"/assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", message_r24.sender.username);\n  }\n}\nfunction MessageChatComponent_div_0_div_19_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 67);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r24 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", message_r24.sender == null ? null : message_r24.sender.username, \" \");\n  }\n}\nfunction MessageChatComponent_div_0_div_19_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 68)(1, \"div\", 69);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 70);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const message_r24 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" R\\u00E9ponse \\u00E0 \", message_r24.replyTo.sender == null ? null : message_r24.replyTo.sender.username, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", message_r24.replyTo.content, \" \");\n  }\n}\nfunction MessageChatComponent_div_0_div_19_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r24 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", message_r24.content, \" \");\n  }\n}\nfunction MessageChatComponent_div_0_div_19_div_7_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 75);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r24 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", message_r24.content, \" \");\n  }\n}\nfunction MessageChatComponent_div_0_div_19_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r44 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 72)(1, \"img\", 73);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_0_div_19_div_7_Template_img_click_1_listener() {\n      i0.ɵɵrestoreView(_r44);\n      const message_r24 = i0.ɵɵnextContext().$implicit;\n      const ctx_r42 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r42.openImageViewer(message_r24.attachments == null ? null : message_r24.attachments[0]));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, MessageChatComponent_div_0_div_19_div_7_div_2_Template, 2, 1, \"div\", 74);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r24 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", message_r24.attachments == null ? null : message_r24.attachments[0] == null ? null : message_r24.attachments[0].url, i0.ɵɵsanitizeUrl)(\"alt\", message_r24.attachments == null ? null : message_r24.attachments[0] == null ? null : message_r24.attachments[0].name);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r24.content);\n  }\n}\nfunction MessageChatComponent_div_0_div_19_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r48 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 76);\n    i0.ɵɵelement(1, \"i\", 77);\n    i0.ɵɵelementStart(2, \"div\", 78)(3, \"div\", 79);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 80);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"button\", 81);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_0_div_19_div_8_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r48);\n      const message_r24 = i0.ɵɵnextContext().$implicit;\n      const ctx_r46 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r46.downloadFile(message_r24.attachments == null ? null : message_r24.attachments[0]));\n    });\n    i0.ɵɵelement(8, \"i\", 82);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const message_r24 = i0.ɵɵnextContext().$implicit;\n    const ctx_r30 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(message_r24.attachments == null ? null : message_r24.attachments[0] == null ? null : message_r24.attachments[0].name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r30.formatFileSize(message_r24.attachments == null ? null : message_r24.attachments[0] == null ? null : message_r24.attachments[0].size), \" \");\n  }\n}\nfunction MessageChatComponent_div_0_div_19_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r52 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 83)(1, \"button\", 84);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_0_div_19_div_9_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r52);\n      const message_r24 = i0.ɵɵnextContext().$implicit;\n      const ctx_r50 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r50.playVoiceMessage(message_r24));\n    });\n    i0.ɵɵelement(2, \"i\", 85);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 86);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const message_r24 = i0.ɵɵnextContext().$implicit;\n    const ctx_r31 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r31.formatDuration(message_r24.attachments == null ? null : message_r24.attachments[0] == null ? null : message_r24.attachments[0].duration), \" \");\n  }\n}\nfunction MessageChatComponent_div_0_div_19_div_10_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 75);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r24 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", message_r24.content, \" \");\n  }\n}\nfunction MessageChatComponent_div_0_div_19_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 87);\n    i0.ɵɵelement(1, \"video\", 88);\n    i0.ɵɵtemplate(2, MessageChatComponent_div_0_div_19_div_10_div_2_Template, 2, 1, \"div\", 74);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r24 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", message_r24.attachments == null ? null : message_r24.attachments[0] == null ? null : message_r24.attachments[0].url, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r24.content);\n  }\n}\nfunction MessageChatComponent_div_0_div_19_div_11_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r61 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 91);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_0_div_19_div_11_span_1_Template_span_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r61);\n      const reaction_r58 = restoredCtx.$implicit;\n      const message_r24 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r59 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r59.reactToMessage(message_r24, reaction_r58.emoji));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const reaction_r58 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", reaction_r58.emoji, \" \", reaction_r58.count, \" \");\n  }\n}\nfunction MessageChatComponent_div_0_div_19_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 89);\n    i0.ɵɵtemplate(1, MessageChatComponent_div_0_div_19_div_11_span_1_Template, 2, 2, \"span\", 90);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r24 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", message_r24.reactions);\n  }\n}\nfunction MessageChatComponent_div_0_div_19_div_15_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 100);\n  }\n}\nfunction MessageChatComponent_div_0_div_19_div_15_i_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 101);\n  }\n}\nfunction MessageChatComponent_div_0_div_19_div_15_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 102);\n  }\n}\nfunction MessageChatComponent_div_0_div_19_div_15_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 103);\n  }\n}\nfunction MessageChatComponent_div_0_div_19_div_15_i_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r71 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"i\", 104);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_0_div_19_div_15_i_6_Template_i_click_0_listener() {\n      i0.ɵɵrestoreView(_r71);\n      const message_r24 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r69 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r69.retryMessage(message_r24));\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageChatComponent_div_0_div_19_div_15_i_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 105);\n  }\n}\nfunction MessageChatComponent_div_0_div_19_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 92)(1, \"div\", 93);\n    i0.ɵɵtemplate(2, MessageChatComponent_div_0_div_19_div_15_i_2_Template, 1, 0, \"i\", 94);\n    i0.ɵɵtemplate(3, MessageChatComponent_div_0_div_19_div_15_i_3_Template, 1, 0, \"i\", 95);\n    i0.ɵɵtemplate(4, MessageChatComponent_div_0_div_19_div_15_i_4_Template, 1, 0, \"i\", 96);\n    i0.ɵɵtemplate(5, MessageChatComponent_div_0_div_19_div_15_i_5_Template, 1, 0, \"i\", 97);\n    i0.ɵɵtemplate(6, MessageChatComponent_div_0_div_19_div_15_i_6_Template, 1, 0, \"i\", 98);\n    i0.ɵɵtemplate(7, MessageChatComponent_div_0_div_19_div_15_i_7_Template, 1, 0, \"i\", 99);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const message_r24 = i0.ɵɵnextContext().$implicit;\n    const ctx_r34 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngSwitch\", ctx_r34.getMessageStatus(message_r24));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngSwitchCase\", \"SENDING\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngSwitchCase\", \"SENT\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngSwitchCase\", \"DELIVERED\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngSwitchCase\", \"read\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngSwitchCase\", \"FAILED\");\n  }\n}\nfunction MessageChatComponent_div_0_div_19_div_23_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r77 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 108);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_0_div_19_div_23_button_1_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r77);\n      const emoji_r74 = restoredCtx.$implicit;\n      const message_r24 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r75 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r75.reactToMessage(message_r24, emoji_r74));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const emoji_r74 = ctx.$implicit;\n    i0.ɵɵproperty(\"title\", \"R\\u00E9agir avec \" + emoji_r74);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", emoji_r74, \" \");\n  }\n}\nfunction MessageChatComponent_div_0_div_19_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 106);\n    i0.ɵɵtemplate(1, MessageChatComponent_div_0_div_19_div_23_button_1_Template, 2, 2, \"button\", 107);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r35 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r35.quickEmojis);\n  }\n}\nfunction MessageChatComponent_div_0_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r79 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 43);\n    i0.ɵɵtemplate(1, MessageChatComponent_div_0_div_19_img_1_Template, 1, 2, \"img\", 44);\n    i0.ɵɵelementStart(2, \"div\", 45);\n    i0.ɵɵtemplate(3, MessageChatComponent_div_0_div_19_div_3_Template, 2, 1, \"div\", 46);\n    i0.ɵɵtemplate(4, MessageChatComponent_div_0_div_19_div_4_Template, 5, 2, \"div\", 47);\n    i0.ɵɵelementStart(5, \"div\", 48);\n    i0.ɵɵtemplate(6, MessageChatComponent_div_0_div_19_div_6_Template, 2, 1, \"div\", 49);\n    i0.ɵɵtemplate(7, MessageChatComponent_div_0_div_19_div_7_Template, 3, 3, \"div\", 50);\n    i0.ɵɵtemplate(8, MessageChatComponent_div_0_div_19_div_8_Template, 9, 2, \"div\", 51);\n    i0.ɵɵtemplate(9, MessageChatComponent_div_0_div_19_div_9_Template, 5, 1, \"div\", 52);\n    i0.ɵɵtemplate(10, MessageChatComponent_div_0_div_19_div_10_Template, 3, 2, \"div\", 53);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, MessageChatComponent_div_0_div_19_div_11_Template, 2, 1, \"div\", 54);\n    i0.ɵɵelementStart(12, \"div\", 55)(13, \"span\", 56);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(15, MessageChatComponent_div_0_div_19_div_15_Template, 8, 6, \"div\", 57);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 58)(17, \"button\", 59);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_0_div_19_Template_button_click_17_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r79);\n      const message_r24 = restoredCtx.$implicit;\n      const ctx_r78 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r78.toggleQuickReactions(message_r24));\n    });\n    i0.ɵɵelement(18, \"i\", 60);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"button\", 61);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_0_div_19_Template_button_click_19_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r79);\n      const message_r24 = restoredCtx.$implicit;\n      const ctx_r80 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r80.setReplyTo(message_r24));\n    });\n    i0.ɵɵelement(20, \"i\", 62);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"button\", 63);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_0_div_19_Template_button_click_21_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r79);\n      const message_r24 = restoredCtx.$implicit;\n      const ctx_r81 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r81.showMessageMenu(message_r24));\n    });\n    i0.ɵɵelement(22, \"i\", 64);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(23, MessageChatComponent_div_0_div_19_div_23_Template, 2, 1, \"div\", 65);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const message_r24 = ctx.$implicit;\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"my-message\", ctx_r9.isMyMessage(message_r24));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r9.isMyMessage(message_r24) && message_r24.sender);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"my-message\", ctx_r9.isMyMessage(message_r24))(\"other-message\", !ctx_r9.isMyMessage(message_r24));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.selectedConversation.isGroup && !ctx_r9.isMyMessage(message_r24));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r24.replyTo);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngSwitch\", message_r24.type);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngSwitchCase\", ctx_r9.MessageType.TEXT);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngSwitchCase\", ctx_r9.MessageType.IMAGE);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngSwitchCase\", ctx_r9.MessageType.FILE);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngSwitchCase\", ctx_r9.MessageType.VOICE_MESSAGE);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngSwitchCase\", ctx_r9.MessageType.VIDEO);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r24.reactions && message_r24.reactions.length > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r9.formatMessageTime(message_r24.timestamp), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.isMyMessage(message_r24));\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.showQuickReactionsFor === message_r24.id);\n  }\n}\nfunction MessageChatComponent_div_0_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 109)(1, \"div\", 110);\n    i0.ɵɵelement(2, \"div\", 111)(3, \"div\", 111)(4, \"div\", 111);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r10.getTypingText());\n  }\n}\nfunction MessageChatComponent_div_0_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r83 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 112)(1, \"div\", 113)(2, \"div\")(3, \"div\", 114);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 115);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"button\", 116);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_0_div_22_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r83);\n      const ctx_r82 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r82.cancelReply());\n    });\n    i0.ɵɵelement(8, \"i\", 117);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" R\\u00E9ponse \\u00E0 \", ctx_r11.replyingTo.sender == null ? null : ctx_r11.replyingTo.sender.username, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r11.replyingTo.content);\n  }\n}\nfunction MessageChatComponent_div_0_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r85 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 112)(1, \"div\", 113)(2, \"div\")(3, \"div\", 118);\n    i0.ɵɵtext(4, \"Modification du message\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 115);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"button\", 116);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_0_div_23_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r85);\n      const ctx_r84 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r84.cancelEditing());\n    });\n    i0.ɵɵelement(8, \"i\", 117);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r12.editingMessage.content);\n  }\n}\nfunction MessageChatComponent_div_0_div_24_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r90 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 122);\n    i0.ɵɵelement(1, \"i\", 123);\n    i0.ɵɵelementStart(2, \"span\", 124);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 125);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_0_div_24_div_2_Template_button_click_4_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r90);\n      const i_r88 = restoredCtx.index;\n      const ctx_r89 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r89.removeSelectedFile(i_r88));\n    });\n    i0.ɵɵelement(5, \"i\", 126);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const file_r87 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(file_r87.name);\n  }\n}\nfunction MessageChatComponent_div_0_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 119)(1, \"div\", 120);\n    i0.ɵɵtemplate(2, MessageChatComponent_div_0_div_24_div_2_Template, 6, 1, \"div\", 121);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r13.selectedFiles);\n  }\n}\nfunction MessageChatComponent_div_0_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r92 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 127);\n    i0.ɵɵelement(1, \"i\", 128);\n    i0.ɵɵelementStart(2, \"span\", 129);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 130);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_0_div_25_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r92);\n      const ctx_r91 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r91.stopVoiceRecording());\n    });\n    i0.ɵɵelement(5, \"i\", 131);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 132);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_0_div_25_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r92);\n      const ctx_r93 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r93.cancelVoiceRecording());\n    });\n    i0.ɵɵelement(7, \"i\", 117);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r14.formatDuration(ctx_r14.recordingDuration));\n  }\n}\nfunction MessageChatComponent_div_0_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r95 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 133)(1, \"button\", 134);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_0_div_31_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r95);\n      const ctx_r94 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r94.openFileSelector());\n    });\n    i0.ɵɵelement(2, \"i\", 123);\n    i0.ɵɵelementStart(3, \"span\", 135);\n    i0.ɵɵtext(4, \"Fichier\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"button\", 134);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_0_div_31_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r95);\n      const ctx_r96 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r96.openFileSelector());\n    });\n    i0.ɵɵelement(6, \"i\", 136);\n    i0.ɵɵelementStart(7, \"span\", 135);\n    i0.ɵɵtext(8, \"Image\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"button\", 134);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_0_div_31_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r95);\n      const ctx_r97 = i0.ɵɵnextContext(2);\n      ctx_r97.showVoiceRecorder = true;\n      return i0.ɵɵresetView(ctx_r97.showAttachmentMenu = false);\n    });\n    i0.ɵɵelement(10, \"i\", 137);\n    i0.ɵɵelementStart(11, \"span\", 135);\n    i0.ɵɵtext(12, \"Audio\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"button\", 134);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_0_div_31_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r95);\n      const ctx_r98 = i0.ɵɵnextContext(2);\n      ctx_r98.showLocationPicker = true;\n      return i0.ɵɵresetView(ctx_r98.showAttachmentMenu = false);\n    });\n    i0.ɵɵelement(14, \"i\", 138);\n    i0.ɵɵelementStart(15, \"span\", 135);\n    i0.ɵɵtext(16, \"Localisation\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction MessageChatComponent_div_0_button_37_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r100 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 139);\n    i0.ɵɵlistener(\"mousedown\", function MessageChatComponent_div_0_button_37_Template_button_mousedown_0_listener() {\n      i0.ɵɵrestoreView(_r100);\n      const ctx_r99 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r99.startVoiceRecording());\n    });\n    i0.ɵɵelement(1, \"i\", 140);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageChatComponent_div_0_button_38_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r102 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 141);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_0_button_38_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r102);\n      const ctx_r101 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r101.sendMessage());\n    });\n    i0.ɵɵelement(1, \"i\", 142);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r18.canSendMessage());\n  }\n}\nfunction MessageChatComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r104 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"div\", 4);\n    i0.ɵɵelement(3, \"img\", 5);\n    i0.ɵɵelementStart(4, \"div\", 6)(5, \"h3\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 7);\n    i0.ɵɵtemplate(8, MessageChatComponent_div_0_span_8_Template, 2, 1, \"span\", 8);\n    i0.ɵɵtemplate(9, MessageChatComponent_div_0_span_9_Template, 2, 0, \"span\", 8);\n    i0.ɵɵtemplate(10, MessageChatComponent_div_0_span_10_Template, 2, 0, \"span\", 8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"div\", 9);\n    i0.ɵɵtemplate(12, MessageChatComponent_div_0_button_12_Template, 2, 0, \"button\", 10);\n    i0.ɵɵtemplate(13, MessageChatComponent_div_0_button_13_Template, 2, 0, \"button\", 11);\n    i0.ɵɵelementStart(14, \"button\", 12);\n    i0.ɵɵelement(15, \"i\", 13);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"div\", 14, 15);\n    i0.ɵɵtemplate(18, MessageChatComponent_div_0_div_18_Template, 2, 0, \"div\", 16);\n    i0.ɵɵtemplate(19, MessageChatComponent_div_0_div_19_Template, 24, 19, \"div\", 17);\n    i0.ɵɵtemplate(20, MessageChatComponent_div_0_div_20_Template, 7, 1, \"div\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 19);\n    i0.ɵɵtemplate(22, MessageChatComponent_div_0_div_22_Template, 9, 2, \"div\", 20);\n    i0.ɵɵtemplate(23, MessageChatComponent_div_0_div_23_Template, 9, 1, \"div\", 20);\n    i0.ɵɵtemplate(24, MessageChatComponent_div_0_div_24_Template, 3, 1, \"div\", 21);\n    i0.ɵɵtemplate(25, MessageChatComponent_div_0_div_25_Template, 8, 1, \"div\", 22);\n    i0.ɵɵelementStart(26, \"div\", 23)(27, \"div\", 24)(28, \"div\", 25)(29, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_0_Template_button_click_29_listener() {\n      i0.ɵɵrestoreView(_r104);\n      const ctx_r103 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r103.toggleAttachmentMenu());\n    });\n    i0.ɵɵelement(30, \"i\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(31, MessageChatComponent_div_0_div_31_Template, 17, 0, \"div\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_0_Template_button_click_32_listener() {\n      i0.ɵɵrestoreView(_r104);\n      const ctx_r105 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r105.toggleEmojiPicker());\n    });\n    i0.ɵɵelement(33, \"i\", 30);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"textarea\", 31, 32);\n    i0.ɵɵlistener(\"ngModelChange\", function MessageChatComponent_div_0_Template_textarea_ngModelChange_34_listener($event) {\n      i0.ɵɵrestoreView(_r104);\n      const ctx_r106 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r106.messageContent = $event);\n    })(\"keydown\", function MessageChatComponent_div_0_Template_textarea_keydown_34_listener($event) {\n      i0.ɵɵrestoreView(_r104);\n      const ctx_r107 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r107.onKeyPress($event));\n    })(\"input\", function MessageChatComponent_div_0_Template_textarea_input_34_listener() {\n      i0.ɵɵrestoreView(_r104);\n      const ctx_r108 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r108.onTyping());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"div\", 24);\n    i0.ɵɵtemplate(37, MessageChatComponent_div_0_button_37_Template, 2, 0, \"button\", 33);\n    i0.ɵɵtemplate(38, MessageChatComponent_div_0_button_38_Template, 2, 1, \"button\", 34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(39, \"input\", 35, 36);\n    i0.ɵɵlistener(\"change\", function MessageChatComponent_div_0_Template_input_change_39_listener($event) {\n      i0.ɵɵrestoreView(_r104);\n      const ctx_r109 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r109.onFileSelected($event));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"online\", !ctx_r0.selectedConversation.isGroup && ctx_r0.isRecipientOnline());\n    i0.ɵɵproperty(\"src\", ctx_r0.selectedConversation.isGroup ? ctx_r0.selectedConversation.groupPhoto : ctx_r0.getRecipientAvatar(), i0.ɵɵsanitizeUrl)(\"alt\", ctx_r0.selectedConversation.isGroup ? ctx_r0.selectedConversation.groupName : ctx_r0.getRecipientName());\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.selectedConversation.isGroup ? ctx_r0.selectedConversation.groupName : ctx_r0.getRecipientName(), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"online\", !ctx_r0.selectedConversation.isGroup && ctx_r0.isRecipientOnline());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.selectedConversation.isGroup);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.selectedConversation.isGroup && ctx_r0.isRecipientOnline());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.selectedConversation.isGroup && !ctx_r0.isRecipientOnline());\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.selectedConversation.isGroup);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.selectedConversation.isGroup);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.messages)(\"ngForTrackBy\", ctx_r0.trackByMessageId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.typingUsers.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.replyingTo);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.editingMessage);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.selectedFiles.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isRecording);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.showAttachmentMenu);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.messageContent)(\"disabled\", ctx_r0.isRecording);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.messageContent.trim() && !ctx_r0.selectedFiles.length && !ctx_r0.isRecording);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.messageContent.trim() || ctx_r0.selectedFiles.length);\n  }\n}\nfunction MessageChatComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 143)(1, \"div\", 144)(2, \"div\", 145)(3, \"div\", 146);\n    i0.ɵɵelement(4, \"i\", 147);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h1\", 148);\n    i0.ɵɵtext(6, \"DevBridge Messages\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 149);\n    i0.ɵɵtext(8, \"Messagerie professionnelle en temps r\\u00E9el\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 150)(10, \"div\", 151)(11, \"div\", 152);\n    i0.ɵɵelement(12, \"i\", 153);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\")(14, \"h4\", 154);\n    i0.ɵɵtext(15, \"Messages en temps r\\u00E9el\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"p\", 155);\n    i0.ɵɵtext(17, \" Conversations instantan\\u00E9es avec notifications \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(18, \"div\", 151)(19, \"div\", 156);\n    i0.ɵɵelement(20, \"i\", 157);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\")(22, \"h4\", 154);\n    i0.ɵɵtext(23, \"Appels audio/vid\\u00E9o\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"p\", 155);\n    i0.ɵɵtext(25, \"Communication directe int\\u00E9gr\\u00E9e\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(26, \"div\", 151)(27, \"div\", 158);\n    i0.ɵɵelement(28, \"i\", 159);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"div\")(30, \"h4\", 154);\n    i0.ɵɵtext(31, \"Partage de fichiers\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"p\", 155);\n    i0.ɵɵtext(33, \"Images, documents et m\\u00E9dias\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(34, \"div\", 160)(35, \"h3\", 161);\n    i0.ɵɵtext(36, \"Comment commencer ?\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"div\", 162)(38, \"p\");\n    i0.ɵɵtext(39, \"\\u2022 S\\u00E9lectionnez une conversation dans la sidebar\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"p\");\n    i0.ɵɵtext(41, \"\\u2022 Ou cliquez sur un contact pour d\\u00E9marrer une discussion\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"p\");\n    i0.ɵɵtext(43, \"\\u2022 Utilisez la recherche pour trouver rapidement\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelement(44, \"app-system-status\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let MessageChatComponent = /*#__PURE__*/(() => {\n  class MessageChatComponent {\n    constructor(messageService, authService, toastService, route, router, cdr, ngZone) {\n      this.messageService = messageService;\n      this.authService = authService;\n      this.toastService = toastService;\n      this.route = route;\n      this.router = router;\n      this.cdr = cdr;\n      this.ngZone = ngZone;\n      // État du composant\n      this.currentUser = null;\n      this.selectedConversation = null;\n      this.messages = [];\n      this.isLoading = false;\n      this.isTyping = false;\n      this.typingUsers = [];\n      // Pagination\n      this.currentPage = 1;\n      this.hasMoreMessages = true;\n      this.loadingMoreMessages = false;\n      // Formulaire de message\n      this.messageContent = '';\n      this.selectedFiles = [];\n      this.isRecording = false;\n      this.recordingDuration = 0;\n      // États UI\n      this.showEmojiPicker = false;\n      this.showAttachmentMenu = false;\n      this.showQuickReactionsFor = null;\n      this.showVoiceRecorder = false;\n      this.showLocationPicker = false;\n      this.replyingTo = null;\n      this.editingMessage = null;\n      // Emojis pour les réactions rapides\n      this.quickEmojis = ['👍', '❤️', '😂', '😮', '😢', '😡'];\n      // Recherche\n      this.searchQuery = '';\n      this.searchResults = [];\n      this.showSearchResults = false;\n      // Subscriptions\n      this.subscriptions = [];\n      // Observables\n      this.conversationId$ = new BehaviorSubject(null);\n      // Constantes\n      this.MessageType = MessageType;\n      this.CallType = CallType;\n    }\n    ngOnInit() {\n      this.initializeComponent();\n      this.setupSubscriptions();\n    }\n    ngAfterViewInit() {\n      this.scrollToBottom();\n    }\n    ngOnDestroy() {\n      this.cleanup();\n    }\n    // ============================================================================\n    // MÉTHODES D'INITIALISATION\n    // ============================================================================\n    initializeComponent() {\n      // Récupérer l'utilisateur actuel\n      this.currentUser = this.authService.getCurrentUser();\n      if (!this.currentUser) {\n        this.router.navigate(['/login']);\n        return;\n      }\n      // Écouter les changements de route pour la conversation\n      this.route.params.subscribe(params => {\n        const conversationId = params['conversationId'];\n        if (conversationId) {\n          this.conversationId$.next(conversationId);\n        }\n      });\n    }\n    setupSubscriptions() {\n      // Subscription pour charger la conversation\n      const conversationSub = this.conversationId$.pipe(filter(id => !!id), distinctUntilChanged(), tap(() => {\n        this.isLoading = true;\n        this.messages = [];\n        this.currentPage = 1;\n        this.hasMoreMessages = true;\n      }), switchMap(conversationId => this.messageService.getConversation(conversationId, 25, 1)), catchError(error => {\n        console.error('Erreur lors du chargement de la conversation:', error);\n        this.toastService.showError('Erreur lors du chargement de la conversation');\n        return of(null);\n      })).subscribe(conversation => {\n        this.isLoading = false;\n        if (conversation) {\n          this.selectedConversation = conversation;\n          this.messages = conversation.messages || [];\n          this.scrollToBottom();\n          this.markMessagesAsRead();\n        }\n        this.cdr.detectChanges();\n      });\n      // Subscription pour les nouveaux messages\n      const messagesSub = this.messageService.subscribeToMessages().subscribe(message => {\n        if (message && this.selectedConversation && message.conversationId === this.selectedConversation.id) {\n          this.addNewMessage(message);\n          this.scrollToBottom();\n          this.markMessageAsRead(message);\n        }\n      });\n      // Subscription pour les indicateurs de frappe\n      const typingSub = this.messageService.subscribeToTypingIndicators().subscribe(event => {\n        if (event && this.selectedConversation && event.conversationId === this.selectedConversation.id) {\n          this.handleTypingIndicator(event);\n        }\n      });\n      this.subscriptions.push(conversationSub, messagesSub, typingSub);\n    }\n    cleanup() {\n      this.subscriptions.forEach(sub => sub.unsubscribe());\n      if (this.typingTimeout) {\n        clearTimeout(this.typingTimeout);\n      }\n      if (this.recordingInterval) {\n        clearInterval(this.recordingInterval);\n      }\n      this.stopTyping();\n    }\n    // ============================================================================\n    // MÉTHODES DE GESTION DES MESSAGES\n    // ============================================================================\n    sendMessage() {\n      if (!this.canSendMessage()) {\n        return;\n      }\n      const content = this.messageContent.trim();\n      const files = this.selectedFiles;\n      // Réinitialiser le formulaire\n      this.messageContent = '';\n      this.selectedFiles = [];\n      this.replyingTo = null;\n      this.stopTyping();\n      if (this.editingMessage) {\n        this.updateMessage(content);\n        return;\n      }\n      // Envoyer le message\n      if (content || files.length > 0) {\n        this.sendNewMessage(content, files);\n      }\n    }\n    canSendMessage() {\n      const hasContent = this.messageContent.trim().length > 0;\n      const hasFiles = this.selectedFiles.length > 0;\n      const hasConversation = !!this.selectedConversation;\n      return hasConversation && (hasContent || hasFiles);\n    }\n    sendNewMessage(content, files) {\n      if (!this.selectedConversation || !this.currentUser) return;\n      const recipientId = this.getRecipientId();\n      if (!recipientId) return;\n      // Créer un message temporaire pour l'affichage immédiat\n      const tempMessage = {\n        id: `temp-${Date.now()}`,\n        content,\n        type: files.length > 0 ? this.getFileMessageType(files[0]) : MessageType.TEXT,\n        timestamp: new Date(),\n        sender: this.currentUser,\n        isPending: true,\n        conversationId: this.selectedConversation.id\n      };\n      this.addNewMessage(tempMessage);\n      this.scrollToBottom();\n      // Envoyer le message via le service\n      const sendObservable = files.length > 0 ? this.messageService.sendMessage(recipientId, content, files[0]) : this.messageService.sendMessage(recipientId, content);\n      sendObservable.subscribe({\n        next: sentMessage => {\n          this.replaceTemporaryMessage(tempMessage.id, sentMessage);\n          this.toastService.showSuccess('Message envoyé');\n        },\n        error: error => {\n          console.error(\"Erreur lors de l'envoi du message:\", error);\n          this.markMessageAsError(tempMessage.id);\n          this.toastService.showError(\"Erreur lors de l'envoi du message\");\n        }\n      });\n    }\n    updateMessage(newContent) {\n      if (!this.editingMessage) return;\n      this.messageService.editMessage(this.editingMessage.id, newContent).subscribe({\n        next: updatedMessage => {\n          this.updateMessageInList(updatedMessage);\n          this.editingMessage = null;\n          this.toastService.showSuccess('Message modifié');\n        },\n        error: error => {\n          console.error('Erreur lors de la modification du message:', error);\n          this.toastService.showError('Erreur lors de la modification du message');\n        }\n      });\n    }\n    deleteMessage(message) {\n      if (!message.id || !this.canDeleteMessage(message)) return;\n      if (confirm('Êtes-vous sûr de vouloir supprimer ce message ?')) {\n        this.messageService.deleteMessage(message.id).subscribe({\n          next: () => {\n            this.removeMessageFromList(message.id);\n            this.toastService.showSuccess('Message supprimé');\n          },\n          error: error => {\n            console.error('Erreur lors de la suppression du message:', error);\n            this.toastService.showError('Erreur lors de la suppression du message');\n          }\n        });\n      }\n    }\n    // ============================================================================\n    // MÉTHODES DE GESTION DES FICHIERS ET MÉDIAS\n    // ============================================================================\n    onFileSelected(event) {\n      const files = event.target.files;\n      if (files && files.length > 0) {\n        this.selectedFiles = Array.from(files);\n        this.showAttachmentMenu = false;\n        // Auto-envoyer si c'est juste un fichier sans texte\n        if (this.messageContent.trim() === '') {\n          this.sendMessage();\n        }\n      }\n    }\n    removeSelectedFile(index) {\n      this.selectedFiles.splice(index, 1);\n    }\n    openFileSelector() {\n      this.fileInput.nativeElement.click();\n    }\n    // ============================================================================\n    // MÉTHODES D'ENREGISTREMENT VOCAL\n    // ============================================================================\n    startVoiceRecording() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        try {\n          const stream = yield navigator.mediaDevices.getUserMedia({\n            audio: true\n          });\n          _this.isRecording = true;\n          _this.recordingDuration = 0;\n          // Démarrer le compteur de durée\n          _this.recordingInterval = setInterval(() => {\n            _this.recordingDuration++;\n          }, 1000);\n          // Ici, vous pouvez implémenter l'enregistrement audio\n          // avec MediaRecorder API\n        } catch (error) {\n          console.error(\"Erreur lors de l'accès au microphone:\", error);\n          _this.toastService.showError(\"Impossible d'accéder au microphone\");\n        }\n      })();\n    }\n    stopVoiceRecording() {\n      this.isRecording = false;\n      if (this.recordingInterval) {\n        clearInterval(this.recordingInterval);\n      }\n      // Ici, vous pouvez traiter l'enregistrement et l'envoyer\n      // comme message vocal\n    }\n\n    cancelVoiceRecording() {\n      this.isRecording = false;\n      this.recordingDuration = 0;\n      if (this.recordingInterval) {\n        clearInterval(this.recordingInterval);\n      }\n    }\n    // ============================================================================\n    // MÉTHODES D'APPELS AUDIO/VIDÉO\n    // ============================================================================\n    startAudioCall() {\n      if (!this.selectedConversation) return;\n      const recipientId = this.getRecipientId();\n      if (!recipientId) return;\n      this.messageService.initiateCall(recipientId, CallType.AUDIO).subscribe({\n        next: call => {\n          this.toastService.showSuccess('Appel audio initié');\n          // Rediriger vers l'interface d'appel\n        },\n\n        error: error => {\n          console.error(\"Erreur lors de l'initiation de l'appel:\", error);\n          this.toastService.showError(\"Erreur lors de l'appel\");\n        }\n      });\n    }\n    startVideoCall() {\n      if (!this.selectedConversation) return;\n      const recipientId = this.getRecipientId();\n      if (!recipientId) return;\n      this.messageService.initiateCall(recipientId, CallType.VIDEO).subscribe({\n        next: call => {\n          this.toastService.showSuccess('Appel vidéo initié');\n          // Rediriger vers l'interface d'appel\n        },\n\n        error: error => {\n          console.error(\"Erreur lors de l'initiation de l'appel vidéo:\", error);\n          this.toastService.showError(\"Erreur lors de l'appel vidéo\");\n        }\n      });\n    }\n    // ============================================================================\n    // MÉTHODES DE GESTION DE LA FRAPPE\n    // ============================================================================\n    onTyping() {\n      if (!this.selectedConversation || this.isTyping) return;\n      this.isTyping = true;\n      this.messageService.startTyping(this.selectedConversation.id).subscribe();\n      // Arrêter la frappe après 3 secondes d'inactivité\n      if (this.typingTimeout) {\n        clearTimeout(this.typingTimeout);\n      }\n      this.typingTimeout = setTimeout(() => {\n        this.stopTyping();\n      }, 3000);\n    }\n    stopTyping() {\n      if (!this.isTyping || !this.selectedConversation) return;\n      this.isTyping = false;\n      this.messageService.stopTyping(this.selectedConversation.id).subscribe();\n      if (this.typingTimeout) {\n        clearTimeout(this.typingTimeout);\n      }\n    }\n    // ============================================================================\n    // MÉTHODES UTILITAIRES\n    // ============================================================================\n    getRecipientId() {\n      if (!this.selectedConversation || !this.currentUser) return null;\n      const participants = this.selectedConversation.participants || [];\n      const currentUserId = this.currentUser.id || this.currentUser._id;\n      const recipient = participants.find(p => (p.id || p._id) !== currentUserId);\n      return recipient ? recipient.id || recipient._id : null;\n    }\n    getFileMessageType(file) {\n      const type = file.type.split('/')[0];\n      switch (type) {\n        case 'image':\n          return MessageType.IMAGE;\n        case 'video':\n          return MessageType.VIDEO;\n        case 'audio':\n          return MessageType.AUDIO;\n        default:\n          return MessageType.FILE;\n      }\n    }\n    addNewMessage(message) {\n      this.messages.push(message);\n      this.cdr.detectChanges();\n    }\n    replaceTemporaryMessage(tempId, realMessage) {\n      const index = this.messages.findIndex(m => m.id === tempId);\n      if (index !== -1) {\n        this.messages[index] = realMessage;\n        this.cdr.detectChanges();\n      }\n    }\n    markMessageAsError(messageId) {\n      const message = this.messages.find(m => m.id === messageId);\n      if (message) {\n        message.isPending = false;\n        message.isError = true;\n        this.cdr.detectChanges();\n      }\n    }\n    updateMessageInList(updatedMessage) {\n      const index = this.messages.findIndex(m => m.id === updatedMessage.id);\n      if (index !== -1) {\n        this.messages[index] = updatedMessage;\n        this.cdr.detectChanges();\n      }\n    }\n    removeMessageFromList(messageId) {\n      this.messages = this.messages.filter(m => m.id !== messageId);\n      this.cdr.detectChanges();\n    }\n    canDeleteMessage(message) {\n      if (!this.currentUser || !message.sender) return false;\n      const currentUserId = this.currentUser.id || this.currentUser._id;\n      const senderId = message.sender.id || message.sender._id;\n      return currentUserId === senderId;\n    }\n    handleTypingIndicator(event) {\n      if (!this.currentUser) return;\n      const currentUserId = this.currentUser.id || this.currentUser._id;\n      if (event.userId === currentUserId) return; // Ignorer ses propres indicateurs\n      if (event.isTyping) {\n        // Ajouter l'utilisateur à la liste des utilisateurs en train de taper\n        const user = this.selectedConversation?.participants?.find(p => (p.id || p._id) === event.userId);\n        if (user && !this.typingUsers.find(u => (u.id || u._id) === event.userId)) {\n          this.typingUsers.push(user);\n        }\n      } else {\n        // Retirer l'utilisateur de la liste\n        this.typingUsers = this.typingUsers.filter(u => (u.id || u._id) !== event.userId);\n      }\n      this.cdr.detectChanges();\n    }\n    markMessagesAsRead() {\n      if (!this.messages.length || !this.currentUser) return;\n      const unreadMessages = this.messages.filter(m => !m.isRead && m.sender && (m.sender.id || m.sender._id) !== (this.currentUser.id || this.currentUser._id));\n      unreadMessages.forEach(message => {\n        if (message.id) {\n          this.markMessageAsRead(message);\n        }\n      });\n    }\n    markMessageAsRead(message) {\n      if (!message.id || message.isRead) return;\n      this.messageService.markMessageAsRead(message.id).subscribe({\n        next: updatedMessage => {\n          this.updateMessageInList(updatedMessage);\n        },\n        error: error => {\n          console.error('Erreur lors du marquage comme lu:', error);\n        }\n      });\n    }\n    scrollToBottom() {\n      this.ngZone.runOutsideAngular(() => {\n        setTimeout(() => {\n          if (this.messagesContainer) {\n            const element = this.messagesContainer.nativeElement;\n            element.scrollTop = element.scrollHeight;\n          }\n        }, 100);\n      });\n    }\n    // ============================================================================\n    // MÉTHODES PUBLIQUES POUR LE TEMPLATE\n    // ============================================================================\n    formatMessageTime(timestamp) {\n      const date = new Date(timestamp);\n      const now = new Date();\n      const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);\n      if (diffInHours < 24) {\n        return date.toLocaleTimeString('fr-FR', {\n          hour: '2-digit',\n          minute: '2-digit'\n        });\n      } else {\n        return date.toLocaleDateString('fr-FR', {\n          day: '2-digit',\n          month: '2-digit'\n        });\n      }\n    }\n    /**\n     * Obtient le statut d'un message pour l'affichage\n     */\n    getMessageStatus(message) {\n      if (message.isPending) return 'SENDING';\n      if (message.isError) return 'FAILED';\n      if (message.status) return message.status;\n      if (message.isRead) return 'READ';\n      if (message.isDelivered) return 'DELIVERED';\n      return 'SENT';\n    }\n    isMyMessage(message) {\n      if (!this.currentUser || !message.sender) return false;\n      const currentUserId = this.currentUser.id || this.currentUser._id;\n      const senderId = message.sender.id || message.sender._id;\n      return currentUserId === senderId;\n    }\n    getTypingText() {\n      if (this.typingUsers.length === 0) return '';\n      if (this.typingUsers.length === 1) {\n        return `${this.typingUsers[0].username} est en train d'écrire...`;\n      } else {\n        return `${this.typingUsers.length} personnes sont en train d'écrire...`;\n      }\n    }\n    onKeyPress(event) {\n      if (event.key === 'Enter' && !event.shiftKey) {\n        event.preventDefault();\n        this.sendMessage();\n      } else {\n        this.onTyping();\n      }\n    }\n    toggleEmojiPicker() {\n      this.showEmojiPicker = !this.showEmojiPicker;\n    }\n    toggleAttachmentMenu() {\n      this.showAttachmentMenu = !this.showAttachmentMenu;\n    }\n    startEditingMessage(message) {\n      this.editingMessage = message;\n      this.messageContent = message.content || '';\n      this.messageInput.nativeElement.focus();\n    }\n    cancelEditing() {\n      this.editingMessage = null;\n      this.messageContent = '';\n    }\n    setReplyTo(message) {\n      this.replyingTo = message;\n      this.messageInput.nativeElement.focus();\n    }\n    cancelReply() {\n      this.replyingTo = null;\n    }\n    // ============================================================================\n    // MÉTHODES POUR LE TEMPLATE (MANQUANTES)\n    // ============================================================================\n    getRecipientName() {\n      if (!this.selectedConversation || !this.currentUser) return '';\n      const participants = this.selectedConversation.participants || [];\n      const currentUserId = this.currentUser.id || this.currentUser._id;\n      const recipient = participants.find(p => (p.id || p._id) !== currentUserId);\n      return recipient?.username || 'Utilisateur inconnu';\n    }\n    getRecipientAvatar() {\n      if (!this.selectedConversation || !this.currentUser) return '/assets/images/default-avatar.png';\n      const participants = this.selectedConversation.participants || [];\n      const currentUserId = this.currentUser.id || this.currentUser._id;\n      const recipient = participants.find(p => (p.id || p._id) !== currentUserId);\n      return recipient?.image || '/assets/images/default-avatar.png';\n    }\n    isRecipientOnline() {\n      if (!this.selectedConversation || !this.currentUser) return false;\n      const participants = this.selectedConversation.participants || [];\n      const currentUserId = this.currentUser.id || this.currentUser._id;\n      const recipient = participants.find(p => (p.id || p._id) !== currentUserId);\n      return recipient?.isOnline || false;\n    }\n    trackByMessageId(index, message) {\n      return message.id || message._id || index.toString();\n    }\n    openImageViewer(attachment) {\n      if (!attachment?.url) return;\n      // Ouvrir l'image dans une nouvelle fenêtre ou modal\n      window.open(attachment.url, '_blank');\n    }\n    formatFileSize(size) {\n      if (!size) return '0 B';\n      const units = ['B', 'KB', 'MB', 'GB'];\n      let unitIndex = 0;\n      let fileSize = size;\n      while (fileSize >= 1024 && unitIndex < units.length - 1) {\n        fileSize /= 1024;\n        unitIndex++;\n      }\n      return `${fileSize.toFixed(1)} ${units[unitIndex]}`;\n    }\n    downloadFile(attachment) {\n      if (!attachment?.url) return;\n      const link = document.createElement('a');\n      link.href = attachment.url;\n      link.download = attachment.name || 'file';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n    }\n    playVoiceMessage(message) {\n      if (!message.attachments?.[0]?.url) return;\n      this.messageService.playAudio(message.attachments[0].url).catch(error => {\n        console.error('Erreur lors de la lecture du message vocal:', error);\n        this.toastService.showError('Erreur lors de la lecture du message vocal');\n      });\n    }\n    formatDuration(duration) {\n      if (!duration) return '0:00';\n      const minutes = Math.floor(duration / 60);\n      const seconds = duration % 60;\n      return `${minutes}:${seconds.toString().padStart(2, '0')}`;\n    }\n    showMessageMenu(message) {\n      // Ici, vous pouvez implémenter un menu contextuel\n      // Pour l'instant, on affiche juste les options disponibles\n      const actions = [];\n      if (this.canDeleteMessage(message)) {\n        actions.push('Supprimer');\n      }\n      if (this.isMyMessage(message)) {\n        actions.push('Modifier');\n      }\n      actions.push('Répondre', 'Transférer', 'Réagir');\n      // Vous pouvez implémenter un vrai menu contextuel ici\n      console.log('Actions disponibles pour ce message:', actions);\n    }\n    /**\n     * Réessaie d'envoyer un message qui a échoué\n     */\n    retryMessage(message) {\n      if (!message.isError || !this.selectedConversation) return;\n      const recipientId = this.getRecipientId();\n      if (!recipientId) return;\n      // Marquer le message comme en cours d'envoi\n      message.isError = false;\n      message.isPending = true;\n      // Réessayer l'envoi\n      const sendObservable = message.attachments?.length ? this.messageService.sendMessage(recipientId, message.content || '', undefined) : this.messageService.sendMessage(recipientId, message.content || '');\n      sendObservable.subscribe({\n        next: sentMessage => {\n          // Remplacer le message temporaire par le message envoyé\n          this.replaceTemporaryMessage(message.id, sentMessage);\n          this.toastService.showSuccess('Message renvoyé avec succès');\n        },\n        error: error => {\n          console.error('Erreur lors du renvoi du message:', error);\n          message.isPending = false;\n          message.isError = true;\n          this.toastService.showError('Échec du renvoi du message');\n        }\n      });\n    }\n    /**\n     * Affiche/masque les réactions rapides pour un message\n     */\n    toggleQuickReactions(message) {\n      const messageId = message.id || message._id;\n      if (this.showQuickReactionsFor === messageId) {\n        this.showQuickReactionsFor = null;\n      } else {\n        this.showQuickReactionsFor = messageId || null;\n      }\n    }\n    /**\n     * Ajoute une réaction à un message\n     */\n    reactToMessage(message, emoji) {\n      if (!this.currentUser || !message.id) return;\n      this.messageService.reactToMessage(message.id, emoji).subscribe({\n        next: updatedMessage => {\n          // Mettre à jour le message dans la liste\n          const index = this.messages.findIndex(m => (m.id || m._id) === (message.id || message._id));\n          if (index !== -1) {\n            this.messages[index] = updatedMessage;\n          }\n          // Masquer les réactions rapides\n          this.showQuickReactionsFor = null;\n          this.toastService.showSuccess(`Réaction ${emoji} ajoutée`);\n        },\n        error: error => {\n          console.error(\"Erreur lors de l'ajout de la réaction:\", error);\n          this.toastService.showError(\"Erreur lors de l'ajout de la réaction\");\n        }\n      });\n    }\n    /**\n     * Gère la completion d'un enregistrement vocal\n     */\n    onVoiceRecordingComplete(recording) {\n      this.showVoiceRecorder = false;\n      if (!this.selectedConversation || !recording) return;\n      const recipientId = this.getRecipientId();\n      if (!recipientId) return;\n      // Créer un FormData pour envoyer le fichier audio\n      const formData = new FormData();\n      formData.append('audio', recording.blob, 'voice-message.webm');\n      formData.append('duration', recording.duration.toString());\n      if (recording.waveform) {\n        formData.append('waveform', JSON.stringify(recording.waveform));\n      }\n      // Envoyer le message vocal\n      this.messageService.sendVoiceMessage(recipientId, formData).subscribe({\n        next: sentMessage => {\n          this.messages.push(sentMessage);\n          this.scrollToBottom();\n          this.toastService.showSuccess('Message vocal envoyé');\n        },\n        error: error => {\n          console.error(\"Erreur lors de l'envoi du message vocal:\", error);\n          this.toastService.showError(\"Erreur lors de l'envoi du message vocal\");\n        }\n      });\n    }\n    /**\n     * Gère la sélection d'une localisation\n     */\n    onLocationSelected(location) {\n      this.showLocationPicker = false;\n      if (!this.selectedConversation || !location) return;\n      const recipientId = this.getRecipientId();\n      if (!recipientId) return;\n      // Créer le contenu du message de localisation\n      const locationMessage = {\n        type: 'location',\n        latitude: location.latitude,\n        longitude: location.longitude,\n        address: location.address,\n        mapUrl: location.mapUrl\n      };\n      // Envoyer le message de localisation\n      this.messageService.sendLocationMessage(recipientId, locationMessage).subscribe({\n        next: sentMessage => {\n          this.messages.push(sentMessage);\n          this.scrollToBottom();\n          this.toastService.showSuccess('Localisation partagée');\n        },\n        error: error => {\n          console.error('Erreur lors du partage de localisation:', error);\n          this.toastService.showError('Erreur lors du partage de localisation');\n        }\n      });\n    }\n    static {\n      this.ɵfac = function MessageChatComponent_Factory(t) {\n        return new (t || MessageChatComponent)(i0.ɵɵdirectiveInject(i1.MessageService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.ToastService), i0.ɵɵdirectiveInject(i4.ActivatedRoute), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.NgZone));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: MessageChatComponent,\n        selectors: [[\"app-message-chat\"]],\n        viewQuery: function MessageChatComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n            i0.ɵɵviewQuery(_c1, 5);\n            i0.ɵɵviewQuery(_c2, 5);\n            i0.ɵɵviewQuery(_c3, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.messagesContainer = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.messageInput = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fileInput = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.voiceRecorder = _t.first);\n          }\n        },\n        decls: 2,\n        vars: 2,\n        consts: [[\"class\", \"chat-container\", 4, \"ngIf\"], [\"class\", \"flex items-center justify-center h-full bg-gray-900 text-gray-400\", 4, \"ngIf\"], [1, \"chat-container\"], [1, \"chat-header\"], [1, \"user-info\"], [1, \"user-avatar\", 3, \"src\", \"alt\"], [1, \"user-details\"], [1, \"user-status\"], [4, \"ngIf\"], [1, \"chat-actions\"], [\"class\", \"action-btn\", \"title\", \"Appel audio\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"action-btn\", \"title\", \"Appel vid\\u00E9o\", 3, \"click\", 4, \"ngIf\"], [\"title\", \"Options\", 1, \"action-btn\"], [1, \"fas\", \"fa-ellipsis-v\"], [1, \"messages-container\"], [\"messagesContainer\", \"\"], [\"class\", \"flex justify-center py-4\", 4, \"ngIf\"], [\"class\", \"message\", 3, \"my-message\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"typing-indicator\", 4, \"ngIf\"], [1, \"message-input-container\"], [\"class\", \"reply-preview\", 4, \"ngIf\"], [\"class\", \"mb-3\", 4, \"ngIf\"], [\"class\", \"recording-indicator mb-3\", 4, \"ngIf\"], [1, \"input-wrapper\"], [1, \"input-actions\"], [1, \"relative\"], [\"title\", \"Pi\\u00E8ce jointe\", 1, \"input-btn\", 3, \"click\"], [1, \"fas\", \"fa-paperclip\"], [\"class\", \"absolute bottom-full left-0 mb-2 bg-gray-800 rounded-lg shadow-lg p-2 space-y-1\", 4, \"ngIf\"], [\"title\", \"Emoji\", 1, \"input-btn\", 3, \"click\"], [1, \"fas\", \"fa-smile\"], [\"placeholder\", \"Tapez votre message...\", \"rows\", \"1\", 1, \"message-input\", 3, \"ngModel\", \"disabled\", \"ngModelChange\", \"keydown\", \"input\"], [\"messageInput\", \"\"], [\"class\", \"input-btn\", \"title\", \"Message vocal\", 3, \"mousedown\", 4, \"ngIf\"], [\"class\", \"send-btn\", \"title\", \"Envoyer\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"type\", \"file\", \"multiple\", \"\", \"accept\", \"image/*,video/*,audio/*,.pdf,.doc,.docx,.txt\", 1, \"hidden\", 3, \"change\"], [\"fileInput\", \"\"], [\"title\", \"Appel audio\", 1, \"action-btn\", 3, \"click\"], [1, \"fas\", \"fa-phone\"], [\"title\", \"Appel vid\\u00E9o\", 1, \"action-btn\", 3, \"click\"], [1, \"fas\", \"fa-video\"], [1, \"flex\", \"justify-center\", \"py-4\"], [1, \"animate-spin\", \"rounded-full\", \"h-8\", \"w-8\", \"border-b-2\", \"border-blue-500\"], [1, \"message\"], [\"class\", \"message-avatar\", 3, \"src\", \"alt\", 4, \"ngIf\"], [1, \"message-content\"], [\"class\", \"text-xs text-blue-400 mb-1 font-medium\", 4, \"ngIf\"], [\"class\", \"reply-preview mb-2\", 4, \"ngIf\"], [3, \"ngSwitch\"], [\"class\", \"message-text\", 4, \"ngSwitchCase\"], [\"class\", \"message-image\", 4, \"ngSwitchCase\"], [\"class\", \"message-file\", 4, \"ngSwitchCase\"], [\"class\", \"voice-message\", 4, \"ngSwitchCase\"], [\"class\", \"message-video\", 4, \"ngSwitchCase\"], [\"class\", \"flex flex-wrap gap-1 mt-2\", 4, \"ngIf\"], [1, \"flex\", \"items-center\", \"justify-between\", \"mt-1\"], [1, \"message-time\"], [\"class\", \"message-status flex items-center space-x-1\", 4, \"ngIf\"], [1, \"message-menu\", \"absolute\", \"top-0\", \"right-0\", \"hidden\", \"group-hover:flex\", \"items-center\", \"space-x-1\"], [\"title\", \"R\\u00E9agir\", 1, \"text-gray-400\", \"hover:text-yellow-400\", \"p-1\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-smile\", \"text-xs\"], [\"title\", \"R\\u00E9pondre\", 1, \"text-gray-400\", \"hover:text-blue-400\", \"p-1\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-reply\", \"text-xs\"], [\"title\", \"Plus d'options\", 1, \"text-gray-400\", \"hover:text-white\", \"p-1\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-ellipsis-h\", \"text-xs\"], [\"class\", \"quick-reactions absolute -top-12 right-0 bg-gray-800 rounded-lg shadow-lg p-2 flex space-x-1 border border-gray-600\", 4, \"ngIf\"], [1, \"message-avatar\", 3, \"src\", \"alt\"], [1, \"text-xs\", \"text-blue-400\", \"mb-1\", \"font-medium\"], [1, \"reply-preview\", \"mb-2\"], [1, \"text-xs\", \"text-gray-400\"], [1, \"text-sm\", \"text-gray-300\", \"truncate\"], [1, \"message-text\"], [1, \"message-image\"], [1, \"message-image\", 3, \"src\", \"alt\", \"click\"], [\"class\", \"message-text mt-2\", 4, \"ngIf\"], [1, \"message-text\", \"mt-2\"], [1, \"message-file\"], [1, \"file-icon\", \"fas\", \"fa-file\"], [1, \"file-info\"], [1, \"file-name\"], [1, \"file-size\"], [1, \"text-blue-400\", \"hover:text-blue-300\", 3, \"click\"], [1, \"fas\", \"fa-download\"], [1, \"voice-message\"], [1, \"voice-play-btn\", 3, \"click\"], [1, \"fas\", \"fa-play\", \"text-white\", \"text-xs\"], [1, \"voice-duration\"], [1, \"message-video\"], [\"controls\", \"\", 1, \"max-w-xs\", \"rounded-lg\", 3, \"src\"], [1, \"flex\", \"flex-wrap\", \"gap-1\", \"mt-2\"], [\"class\", \"text-xs bg-gray-600 rounded-full px-2 py-1 cursor-pointer\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"text-xs\", \"bg-gray-600\", \"rounded-full\", \"px-2\", \"py-1\", \"cursor-pointer\", 3, \"click\"], [1, \"message-status\", \"flex\", \"items-center\", \"space-x-1\"], [1, \"flex\", \"items-center\", 3, \"ngSwitch\"], [\"class\", \"fas fa-clock text-gray-400 animate-pulse text-xs\", \"title\", \"Envoi en cours...\", 4, \"ngSwitchCase\"], [\"class\", \"fas fa-check text-gray-400 text-xs\", \"title\", \"Envoy\\u00E9\", 4, \"ngSwitchCase\"], [\"class\", \"fas fa-check-double text-gray-400 text-xs\", \"title\", \"Livr\\u00E9\", 4, \"ngSwitchCase\"], [\"class\", \"fas fa-check-double text-blue-400 text-xs\", \"title\", \"Lu\", 4, \"ngSwitchCase\"], [\"class\", \"fas fa-exclamation-triangle text-red-400 text-xs cursor-pointer\", \"title\", \"\\u00C9chec d'envoi - Cliquer pour r\\u00E9essayer\", 3, \"click\", 4, \"ngSwitchCase\"], [\"class\", \"fas fa-check text-gray-400 text-xs\", 4, \"ngSwitchDefault\"], [\"title\", \"Envoi en cours...\", 1, \"fas\", \"fa-clock\", \"text-gray-400\", \"animate-pulse\", \"text-xs\"], [\"title\", \"Envoy\\u00E9\", 1, \"fas\", \"fa-check\", \"text-gray-400\", \"text-xs\"], [\"title\", \"Livr\\u00E9\", 1, \"fas\", \"fa-check-double\", \"text-gray-400\", \"text-xs\"], [\"title\", \"Lu\", 1, \"fas\", \"fa-check-double\", \"text-blue-400\", \"text-xs\"], [\"title\", \"\\u00C9chec d'envoi - Cliquer pour r\\u00E9essayer\", 1, \"fas\", \"fa-exclamation-triangle\", \"text-red-400\", \"text-xs\", \"cursor-pointer\", 3, \"click\"], [1, \"fas\", \"fa-check\", \"text-gray-400\", \"text-xs\"], [1, \"quick-reactions\", \"absolute\", \"-top-12\", \"right-0\", \"bg-gray-800\", \"rounded-lg\", \"shadow-lg\", \"p-2\", \"flex\", \"space-x-1\", \"border\", \"border-gray-600\"], [\"class\", \"hover:bg-gray-700 rounded p-1 transition-colors text-lg\", 3, \"title\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"hover:bg-gray-700\", \"rounded\", \"p-1\", \"transition-colors\", \"text-lg\", 3, \"title\", \"click\"], [1, \"typing-indicator\"], [1, \"typing-dots\"], [1, \"typing-dot\"], [1, \"reply-preview\"], [1, \"reply-header\"], [1, \"text-xs\", \"text-blue-400\"], [1, \"reply-text\"], [1, \"text-gray-400\", \"hover:text-white\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"text-xs\", \"text-yellow-400\"], [1, \"mb-3\"], [1, \"flex\", \"flex-wrap\", \"gap-2\"], [\"class\", \"flex items-center space-x-2 bg-gray-700 rounded-lg p-2\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"items-center\", \"space-x-2\", \"bg-gray-700\", \"rounded-lg\", \"p-2\"], [1, \"fas\", \"fa-file\", \"text-blue-400\"], [1, \"text-sm\", \"text-white\", \"truncate\", \"max-w-32\"], [1, \"text-red-400\", \"hover:text-red-300\", 3, \"click\"], [1, \"fas\", \"fa-times\", \"text-xs\"], [1, \"recording-indicator\", \"mb-3\"], [1, \"fas\", \"fa-microphone\", \"text-white\"], [1, \"recording-time\"], [1, \"text-white\", \"hover:text-gray-300\", 3, \"click\"], [1, \"fas\", \"fa-stop\"], [1, \"text-white\", \"hover:text-gray-300\", \"ml-2\", 3, \"click\"], [1, \"absolute\", \"bottom-full\", \"left-0\", \"mb-2\", \"bg-gray-800\", \"rounded-lg\", \"shadow-lg\", \"p-2\", \"space-y-1\"], [1, \"flex\", \"items-center\", \"space-x-2\", \"w-full\", \"p-2\", \"hover:bg-gray-700\", \"rounded\", \"text-left\", 3, \"click\"], [1, \"text-white\", \"text-sm\"], [1, \"fas\", \"fa-image\", \"text-green-400\"], [1, \"fas\", \"fa-microphone\", \"text-red-400\"], [1, \"fas\", \"fa-map-marker-alt\", \"text-purple-400\"], [\"title\", \"Message vocal\", 1, \"input-btn\", 3, \"mousedown\"], [1, \"fas\", \"fa-microphone\"], [\"title\", \"Envoyer\", 1, \"send-btn\", 3, \"disabled\", \"click\"], [1, \"fas\", \"fa-paper-plane\", \"text-white\"], [1, \"flex\", \"items-center\", \"justify-center\", \"h-full\", \"bg-gray-900\", \"text-gray-400\"], [1, \"text-center\", \"max-w-md\", \"mx-auto\", \"p-8\"], [1, \"mb-8\"], [1, \"w-24\", \"h-24\", \"mx-auto\", \"bg-gradient-to-br\", \"from-blue-500\", \"to-purple-600\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\", \"mb-4\"], [1, \"fas\", \"fa-comments\", \"text-3xl\", \"text-white\"], [1, \"text-3xl\", \"font-bold\", \"text-white\", \"mb-2\"], [1, \"text-gray-400\"], [1, \"space-y-4\", \"mb-8\"], [1, \"flex\", \"items-center\", \"space-x-3\", \"text-left\"], [1, \"w-10\", \"h-10\", \"bg-blue-600\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\"], [1, \"fas\", \"fa-bolt\", \"text-white\", \"text-sm\"], [1, \"text-white\", \"font-medium\"], [1, \"text-sm\", \"text-gray-400\"], [1, \"w-10\", \"h-10\", \"bg-green-600\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\"], [1, \"fas\", \"fa-phone\", \"text-white\", \"text-sm\"], [1, \"w-10\", \"h-10\", \"bg-purple-600\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\"], [1, \"fas\", \"fa-file\", \"text-white\", \"text-sm\"], [1, \"bg-gray-800\", \"rounded-lg\", \"p-6\", \"border\", \"border-gray-700\", \"mb-6\"], [1, \"text-lg\", \"font-semibold\", \"text-white\", \"mb-3\"], [1, \"space-y-2\", \"text-sm\", \"text-gray-300\"]],\n        template: function MessageChatComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵtemplate(0, MessageChatComponent_div_0_Template, 41, 25, \"div\", 0);\n            i0.ɵɵtemplate(1, MessageChatComponent_div_1_Template, 45, 0, \"div\", 1);\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"ngIf\", ctx.selectedConversation);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.selectedConversation);\n          }\n        },\n        dependencies: [i5.NgForOf, i5.NgIf, i5.NgSwitch, i5.NgSwitchCase, i5.NgSwitchDefault, i6.DefaultValueAccessor, i6.NgControlStatus, i6.NgModel, i7.SystemStatusComponent],\n        styles: [\".chat-container[_ngcontent-%COMP%]{display:flex;height:100%;flex-direction:column;--tw-bg-opacity: 1;background-color:rgb(17 24 39 / var(--tw-bg-opacity, 1));--tw-text-opacity: 1;color:rgb(255 255 255 / var(--tw-text-opacity, 1))}.chat-header[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;border-bottom-width:1px;--tw-border-opacity: 1;border-color:rgb(55 65 81 / var(--tw-border-opacity, 1));--tw-bg-opacity: 1;background-color:rgb(31 41 55 / var(--tw-bg-opacity, 1));padding:1rem;background:linear-gradient(135deg,#1f2937 0%,#111827 100%);box-shadow:0 2px 10px #0000004d}.user-info[_ngcontent-%COMP%]{display:flex;align-items:center}.user-info[_ngcontent-%COMP%] > [_ngcontent-%COMP%]:not([hidden]) ~ [_ngcontent-%COMP%]:not([hidden]){--tw-space-x-reverse: 0;margin-right:calc(.75rem * var(--tw-space-x-reverse));margin-left:calc(.75rem * calc(1 - var(--tw-space-x-reverse)))}.user-avatar[_ngcontent-%COMP%]{height:2.5rem;width:2.5rem;border-radius:9999px;border-width:2px;--tw-border-opacity: 1;border-color:rgb(59 130 246 / var(--tw-border-opacity, 1));box-shadow:0 0 10px #3b82f680}.user-details[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-weight:600;--tw-text-opacity: 1;color:rgb(255 255 255 / var(--tw-text-opacity, 1))}.user-status[_ngcontent-%COMP%]{font-size:.875rem;line-height:1.25rem;--tw-text-opacity: 1;color:rgb(156 163 175 / var(--tw-text-opacity, 1))}.user-status.online[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(74 222 128 / var(--tw-text-opacity, 1))}.chat-actions[_ngcontent-%COMP%]{display:flex;align-items:center}.chat-actions[_ngcontent-%COMP%] > [_ngcontent-%COMP%]:not([hidden]) ~ [_ngcontent-%COMP%]:not([hidden]){--tw-space-x-reverse: 0;margin-right:calc(.5rem * var(--tw-space-x-reverse));margin-left:calc(.5rem * calc(1 - var(--tw-space-x-reverse)))}.action-btn[_ngcontent-%COMP%]{border-radius:9999px;--tw-bg-opacity: 1;background-color:rgb(55 65 81 / var(--tw-bg-opacity, 1));padding:.5rem;transition-property:all;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.2s}.action-btn[_ngcontent-%COMP%]:hover{--tw-bg-opacity: 1;background-color:rgb(75 85 99 / var(--tw-bg-opacity, 1))}.action-btn[_ngcontent-%COMP%]{border:1px solid rgba(59,130,246,.3)}.action-btn[_ngcontent-%COMP%]:hover{box-shadow:0 0 15px #3b82f666;border-color:#3b82f699}.action-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(96 165 250 / var(--tw-text-opacity, 1))}.messages-container[_ngcontent-%COMP%]{flex:1 1 0%}.messages-container[_ngcontent-%COMP%] > [_ngcontent-%COMP%]:not([hidden]) ~ [_ngcontent-%COMP%]:not([hidden]){--tw-space-y-reverse: 0;margin-top:calc(1rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(1rem * var(--tw-space-y-reverse))}.messages-container[_ngcontent-%COMP%]{overflow-y:auto;padding:1rem;background:linear-gradient(180deg,#111827 0%,#0f172a 100%);scrollbar-width:thin;scrollbar-color:#374151 #1f2937}.messages-container[_ngcontent-%COMP%]::-webkit-scrollbar{width:6px}.messages-container[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:#1f2937}.messages-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#374151;border-radius:3px}.messages-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#4b5563}.message[_ngcontent-%COMP%]{display:flex;max-width:20rem;align-items:flex-end}.message[_ngcontent-%COMP%] > [_ngcontent-%COMP%]:not([hidden]) ~ [_ngcontent-%COMP%]:not([hidden]){--tw-space-x-reverse: 0;margin-right:calc(.5rem * var(--tw-space-x-reverse));margin-left:calc(.5rem * calc(1 - var(--tw-space-x-reverse)))}@media (min-width: 768px){.message[_ngcontent-%COMP%]{max-width:28rem}}.message[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_messageSlideIn .3s ease-out}@keyframes _ngcontent-%COMP%_messageSlideIn{0%{opacity:0;transform:translateY(10px)}to{opacity:1;transform:translateY(0)}}.message.my-message[_ngcontent-%COMP%]{margin-left:auto;flex-direction:row-reverse}.message.my-message[_ngcontent-%COMP%] > [_ngcontent-%COMP%]:not([hidden]) ~ [_ngcontent-%COMP%]:not([hidden]){--tw-space-x-reverse: 1}.message-avatar[_ngcontent-%COMP%]{height:2rem;width:2rem;border-radius:9999px;border-width:1px;--tw-border-opacity: 1;border-color:rgb(75 85 99 / var(--tw-border-opacity, 1))}.message-content[_ngcontent-%COMP%]{max-width:100%;overflow-wrap:break-word;border-radius:1rem;padding:.5rem 1rem;position:relative}.message-content.my-message[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(37 99 235 / var(--tw-bg-opacity, 1));--tw-text-opacity: 1;color:rgb(255 255 255 / var(--tw-text-opacity, 1));background:linear-gradient(135deg,#3b82f6 0%,#1d4ed8 100%);box-shadow:0 2px 10px #3b82f64d}.message-content.other-message[_ngcontent-%COMP%]{border-width:1px;--tw-border-opacity: 1;border-color:rgb(75 85 99 / var(--tw-border-opacity, 1));--tw-bg-opacity: 1;background-color:rgb(55 65 81 / var(--tw-bg-opacity, 1));--tw-text-opacity: 1;color:rgb(255 255 255 / var(--tw-text-opacity, 1));background:linear-gradient(135deg,#374151 0%,#1f2937 100%)}.message-text[_ngcontent-%COMP%]{font-size:.875rem;line-height:1.25rem;line-height:1.625}.message-time[_ngcontent-%COMP%]{margin-top:.25rem;display:block;font-size:.75rem;line-height:1rem;--tw-text-opacity: 1;color:rgb(156 163 175 / var(--tw-text-opacity, 1))}.message-status[_ngcontent-%COMP%]{margin-top:.25rem;font-size:.75rem;line-height:1rem;--tw-text-opacity: 1;color:rgb(156 163 175 / var(--tw-text-opacity, 1))}.message-status.read[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(96 165 250 / var(--tw-text-opacity, 1))}.message-status.pending[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(250 204 21 / var(--tw-text-opacity, 1))}.message-status.error[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(248 113 113 / var(--tw-text-opacity, 1))}.message-image[_ngcontent-%COMP%]{max-width:20rem;cursor:pointer;border-radius:.5rem;box-shadow:0 4px 15px #0000004d}.message-file[_ngcontent-%COMP%]{display:flex;align-items:center}.message-file[_ngcontent-%COMP%] > [_ngcontent-%COMP%]:not([hidden]) ~ [_ngcontent-%COMP%]:not([hidden]){--tw-space-x-reverse: 0;margin-right:calc(.5rem * var(--tw-space-x-reverse));margin-left:calc(.5rem * calc(1 - var(--tw-space-x-reverse)))}.message-file[_ngcontent-%COMP%]{border-radius:.5rem;border-width:1px;--tw-border-opacity: 1;border-color:rgb(75 85 99 / var(--tw-border-opacity, 1));--tw-bg-opacity: 1;background-color:rgb(31 41 55 / var(--tw-bg-opacity, 1));padding:.75rem}.file-icon[_ngcontent-%COMP%]{font-size:1.25rem;line-height:1.75rem;--tw-text-opacity: 1;color:rgb(96 165 250 / var(--tw-text-opacity, 1))}.file-info[_ngcontent-%COMP%]{flex:1 1 0%}.file-name[_ngcontent-%COMP%]{font-size:.875rem;line-height:1.25rem;font-weight:500;--tw-text-opacity: 1;color:rgb(255 255 255 / var(--tw-text-opacity, 1))}.file-size[_ngcontent-%COMP%]{font-size:.75rem;line-height:1rem;--tw-text-opacity: 1;color:rgb(156 163 175 / var(--tw-text-opacity, 1))}.voice-message[_ngcontent-%COMP%]{display:flex;align-items:center}.voice-message[_ngcontent-%COMP%] > [_ngcontent-%COMP%]:not([hidden]) ~ [_ngcontent-%COMP%]:not([hidden]){--tw-space-x-reverse: 0;margin-right:calc(.75rem * var(--tw-space-x-reverse));margin-left:calc(.75rem * calc(1 - var(--tw-space-x-reverse)))}.voice-message[_ngcontent-%COMP%]{border-radius:.5rem;--tw-bg-opacity: 1;background-color:rgb(31 41 55 / var(--tw-bg-opacity, 1));padding:.75rem}.voice-play-btn[_ngcontent-%COMP%]{display:flex;height:2rem;width:2rem;cursor:pointer;align-items:center;justify-content:center;border-radius:9999px;--tw-bg-opacity: 1;background-color:rgb(37 99 235 / var(--tw-bg-opacity, 1))}.voice-duration[_ngcontent-%COMP%]{font-size:.875rem;line-height:1.25rem;--tw-text-opacity: 1;color:rgb(156 163 175 / var(--tw-text-opacity, 1))}.typing-indicator[_ngcontent-%COMP%]{display:flex;align-items:center}.typing-indicator[_ngcontent-%COMP%] > [_ngcontent-%COMP%]:not([hidden]) ~ [_ngcontent-%COMP%]:not([hidden]){--tw-space-x-reverse: 0;margin-right:calc(.5rem * var(--tw-space-x-reverse));margin-left:calc(.5rem * calc(1 - var(--tw-space-x-reverse)))}.typing-indicator[_ngcontent-%COMP%]{padding:.75rem;font-size:.875rem;line-height:1.25rem;--tw-text-opacity: 1;color:rgb(156 163 175 / var(--tw-text-opacity, 1))}.typing-dots[_ngcontent-%COMP%]{display:flex}.typing-dots[_ngcontent-%COMP%] > [_ngcontent-%COMP%]:not([hidden]) ~ [_ngcontent-%COMP%]:not([hidden]){--tw-space-x-reverse: 0;margin-right:calc(.25rem * var(--tw-space-x-reverse));margin-left:calc(.25rem * calc(1 - var(--tw-space-x-reverse)))}.typing-dot[_ngcontent-%COMP%]{height:.5rem;width:.5rem;border-radius:9999px;--tw-bg-opacity: 1;background-color:rgb(156 163 175 / var(--tw-bg-opacity, 1));animation:_ngcontent-%COMP%_typingPulse 1.4s infinite ease-in-out}.typing-dot[_ngcontent-%COMP%]:nth-child(1){animation-delay:-.32s}.typing-dot[_ngcontent-%COMP%]:nth-child(2){animation-delay:-.16s}@keyframes _ngcontent-%COMP%_typingPulse{0%,80%,to{transform:scale(.8);opacity:.5}40%{transform:scale(1);opacity:1}}.message-input-container[_ngcontent-%COMP%]{border-top-width:1px;--tw-border-opacity: 1;border-color:rgb(55 65 81 / var(--tw-border-opacity, 1));--tw-bg-opacity: 1;background-color:rgb(31 41 55 / var(--tw-bg-opacity, 1));padding:1rem;background:linear-gradient(135deg,#1f2937 0%,#111827 100%)}.reply-preview[_ngcontent-%COMP%]{margin-bottom:.75rem;border-radius:.5rem;border-left-width:4px;--tw-border-opacity: 1;border-color:rgb(59 130 246 / var(--tw-border-opacity, 1));--tw-bg-opacity: 1;background-color:rgb(55 65 81 / var(--tw-bg-opacity, 1));padding:.5rem}.reply-header[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between}.reply-text[_ngcontent-%COMP%]{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;font-size:.875rem;line-height:1.25rem;--tw-text-opacity: 1;color:rgb(209 213 219 / var(--tw-text-opacity, 1))}.input-wrapper[_ngcontent-%COMP%]{display:flex;align-items:flex-end}.input-wrapper[_ngcontent-%COMP%] > [_ngcontent-%COMP%]:not([hidden]) ~ [_ngcontent-%COMP%]:not([hidden]){--tw-space-x-reverse: 0;margin-right:calc(.5rem * var(--tw-space-x-reverse));margin-left:calc(.5rem * calc(1 - var(--tw-space-x-reverse)))}.message-input[_ngcontent-%COMP%]{flex:1 1 0%;resize:none;border-radius:1rem;border-width:1px;--tw-border-opacity: 1;border-color:rgb(75 85 99 / var(--tw-border-opacity, 1));--tw-bg-opacity: 1;background-color:rgb(55 65 81 / var(--tw-bg-opacity, 1));padding:.5rem 1rem;--tw-text-opacity: 1;color:rgb(255 255 255 / var(--tw-text-opacity, 1))}.message-input[_ngcontent-%COMP%]::placeholder{--tw-placeholder-opacity: 1;color:rgb(156 163 175 / var(--tw-placeholder-opacity, 1))}.message-input[_ngcontent-%COMP%]{min-height:40px;max-height:120px;transition:all .2s ease}.message-input[_ngcontent-%COMP%]:focus{--tw-border-opacity: 1;border-color:rgb(59 130 246 / var(--tw-border-opacity, 1));outline:2px solid transparent;outline-offset:2px;box-shadow:0 0 10px #3b82f64d}.input-actions[_ngcontent-%COMP%]{display:flex;align-items:center}.input-actions[_ngcontent-%COMP%] > [_ngcontent-%COMP%]:not([hidden]) ~ [_ngcontent-%COMP%]:not([hidden]){--tw-space-x-reverse: 0;margin-right:calc(.25rem * var(--tw-space-x-reverse));margin-left:calc(.25rem * calc(1 - var(--tw-space-x-reverse)))}.input-btn[_ngcontent-%COMP%]{border-radius:9999px;--tw-bg-opacity: 1;background-color:rgb(55 65 81 / var(--tw-bg-opacity, 1));padding:.5rem;transition-property:all;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.2s}.input-btn[_ngcontent-%COMP%]:hover{--tw-bg-opacity: 1;background-color:rgb(75 85 99 / var(--tw-bg-opacity, 1))}.input-btn[_ngcontent-%COMP%]{border:1px solid rgba(59,130,246,.3)}.input-btn[_ngcontent-%COMP%]:hover{box-shadow:0 0 10px #3b82f666}.input-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(96 165 250 / var(--tw-text-opacity, 1))}.send-btn[_ngcontent-%COMP%]{border-radius:9999px;--tw-bg-opacity: 1;background-color:rgb(37 99 235 / var(--tw-bg-opacity, 1));padding:.5rem;transition-property:all;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.2s}.send-btn[_ngcontent-%COMP%]:hover{--tw-bg-opacity: 1;background-color:rgb(29 78 216 / var(--tw-bg-opacity, 1))}.send-btn[_ngcontent-%COMP%]{box-shadow:0 2px 10px #3b82f64d}.send-btn[_ngcontent-%COMP%]:hover{box-shadow:0 4px 15px #3b82f680}.send-btn[_ngcontent-%COMP%]:disabled{cursor:not-allowed;--tw-bg-opacity: 1;background-color:rgb(75 85 99 / var(--tw-bg-opacity, 1));box-shadow:none}.recording-indicator[_ngcontent-%COMP%]{display:flex;align-items:center}.recording-indicator[_ngcontent-%COMP%] > [_ngcontent-%COMP%]:not([hidden]) ~ [_ngcontent-%COMP%]:not([hidden]){--tw-space-x-reverse: 0;margin-right:calc(.5rem * var(--tw-space-x-reverse));margin-left:calc(.5rem * calc(1 - var(--tw-space-x-reverse)))}.recording-indicator[_ngcontent-%COMP%]{border-radius:.5rem;--tw-bg-opacity: 1;background-color:rgb(220 38 38 / var(--tw-bg-opacity, 1));padding:.75rem;animation:_ngcontent-%COMP%_recordingPulse 1s infinite}@keyframes _ngcontent-%COMP%_recordingPulse{0%,to{opacity:1}50%{opacity:.7}}.recording-time[_ngcontent-%COMP%]{font-family:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,Liberation Mono,Courier New,monospace;--tw-text-opacity: 1;color:rgb(255 255 255 / var(--tw-text-opacity, 1))}.message-menu[_ngcontent-%COMP%]{opacity:0;transition-property:opacity;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.2s}.message[_ngcontent-%COMP%]:hover   .message-menu[_ngcontent-%COMP%]{opacity:1}.quick-reactions[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeInUp .2s ease-out}@keyframes _ngcontent-%COMP%_fadeInUp{0%{opacity:0;transform:translateY(10px)}to{opacity:1;transform:translateY(0)}}.reaction-emoji[_ngcontent-%COMP%]{transition-property:transform;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.2s}.reaction-emoji[_ngcontent-%COMP%]:hover{transform:scale(1.2)}.message-status[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.2s}.message-status[_ngcontent-%COMP%]   i.animate-pulse[_ngcontent-%COMP%]{animation:pulse 2s infinite}.animate-fadeIn[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeIn .3s ease-out}@keyframes _ngcontent-%COMP%_fadeIn{0%{opacity:0;transform:translateY(-10px)}to{opacity:1;transform:translateY(0)}}@media (max-width: 768px){.message[_ngcontent-%COMP%]{max-width:20rem}.chat-header[_ngcontent-%COMP%]{padding:.5rem .75rem}.messages-container[_ngcontent-%COMP%], .message-input-container[_ngcontent-%COMP%]{padding-left:.75rem;padding-right:.75rem}.quick-reactions[_ngcontent-%COMP%]{top:-2.5rem;font-size:.875rem;line-height:1.25rem}}\"]\n      });\n    }\n  }\n  return MessageChatComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}