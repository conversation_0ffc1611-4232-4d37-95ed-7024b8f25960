import { Component, OnInit } from '@angular/core';
import { AdvancedNotificationService } from '../../../services/advanced-notification.service';

@Component({
  selector: 'app-test-page',
  templateUrl: './test-page.component.html',
  styleUrls: ['./test-page.component.css']
})
export class TestPageComponent implements OnInit {
  currentUserId = 'user123';
  selectedConversationId = '1';

  constructor(private notificationService: AdvancedNotificationService) {}

  ngOnInit(): void {
    // Initialiser avec quelques notifications de test
    this.showWelcomeNotifications();
  }

  private showWelcomeNotifications(): void {
    setTimeout(() => {
      this.notificationService.success(
        'Bienvenue!',
        'Le système de notifications et de chat est maintenant opérationnel.'
      );
    }, 1000);

    setTimeout(() => {
      this.notificationService.showFromTemplate('message_received', {
        sender: '<PERSON>',
        message: 'Salut! Comment ça va?'
      });
    }, 3000);
  }

  // Méthodes de test pour les notifications
  testSuccessNotification(): void {
    this.notificationService.success(
      'Opération réussie',
      'Votre action a été effectuée avec succès!'
    );
  }

  testErrorNotification(): void {
    this.notificationService.error(
      'Erreur système',
      'Une erreur inattendue s\'est produite. Veuillez réessayer.'
    );
  }

  testWarningNotification(): void {
    this.notificationService.warning(
      'Attention',
      'Cette action nécessite votre confirmation avant de continuer.'
    );
  }

  testInfoNotification(): void {
    this.notificationService.info(
      'Information',
      'Nouvelle mise à jour disponible. Cliquez pour en savoir plus.'
    );
  }

  testMessageTemplate(): void {
    this.notificationService.showFromTemplate('message_received', {
      sender: 'Alice Smith',
      message: 'Nouveau message de test!'
    });
  }

  testCallTemplate(): void {
    this.notificationService.showFromTemplate('call_incoming', {
      caller: 'Bob Johnson'
    });
  }

  testSystemTemplate(): void {
    this.notificationService.showFromTemplate('system_update', {
      version: '2.1.0'
    });
  }

  testProgressNotification(): void {
    const notificationId = this.notificationService.show({
      type: 'info',
      title: 'Téléchargement en cours',
      message: 'Téléchargement du fichier...',
      persistent: true,
      progress: 0
    });

    // Simuler la progression
    let progress = 0;
    const interval = setInterval(() => {
      progress += 10;
      this.notificationService.updateNotification(notificationId, {
        progress,
        message: `Téléchargement du fichier... ${progress}%`
      });

      if (progress >= 100) {
        clearInterval(interval);
        setTimeout(() => {
          this.notificationService.updateNotification(notificationId, {
            type: 'success',
            title: 'Téléchargement terminé',
            message: 'Le fichier a été téléchargé avec succès!',
            progress: undefined,
            persistent: false,
            duration: 3000
          });
        }, 500);
      }
    }, 500);
  }

  testCustomNotification(): void {
    this.notificationService.show({
      type: 'custom',
      title: 'Notification personnalisée',
      message: 'Ceci est une notification avec des actions personnalisées.',
      icon: 'fas fa-star',
      persistent: true,
      priority: 'high',
      actions: [
        {
          id: 'accept',
          title: 'Accepter',
          icon: 'fas fa-check',
          style: 'primary',
          action: () => {
            this.notificationService.success('Accepté', 'Action acceptée avec succès!');
          }
        },
        {
          id: 'decline',
          title: 'Refuser',
          icon: 'fas fa-times',
          style: 'danger',
          action: () => {
            this.notificationService.warning('Refusé', 'Action refusée.');
          }
        },
        {
          id: 'later',
          title: 'Plus tard',
          icon: 'fas fa-clock',
          style: 'secondary',
          action: () => {
            this.notificationService.info('Reporté', 'Action reportée à plus tard.');
          }
        }
      ]
    });
  }

  clearAllNotifications(): void {
    this.notificationService.dismissAll();
  }

  // Méthodes pour tester les paramètres
  toggleNotifications(): void {
    const currentSettings = this.notificationService.getSettings();
    this.notificationService.updateSettings({
      enabled: !currentSettings.enabled
    });
    
    const status = currentSettings.enabled ? 'désactivées' : 'activées';
    this.notificationService.info(
      'Paramètres mis à jour',
      `Les notifications ont été ${status}.`
    );
  }

  toggleSound(): void {
    const currentSettings = this.notificationService.getSettings();
    this.notificationService.updateSettings({
      soundEnabled: !currentSettings.soundEnabled
    });
    
    const status = currentSettings.soundEnabled ? 'désactivé' : 'activé';
    this.notificationService.info(
      'Paramètres audio',
      `Le son des notifications a été ${status}.`
    );
  }

  toggleBrowserNotifications(): void {
    const currentSettings = this.notificationService.getSettings();
    this.notificationService.updateSettings({
      browserNotifications: !currentSettings.browserNotifications
    });
    
    const status = currentSettings.browserNotifications ? 'désactivées' : 'activées';
    this.notificationService.info(
      'Notifications navigateur',
      `Les notifications navigateur ont été ${status}.`
    );
  }
}
