{"ast": null, "code": "import { trigger, transition, style, animate, query, stagger } from '@angular/animations';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/reunion.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/services/authuser.service\";\nimport * as i4 from \"@angular/platform-browser\";\nimport * as i5 from \"src/app/services/toast.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/forms\";\nimport * as i8 from \"../../../../shared/pipes/highlight-presence.pipe\";\nfunction ReunionListComponent_div_10_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function ReunionListComponent_div_10_button_9_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r9.clearSearch());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 20);\n    i0.ɵɵelement(2, \"path\", 31);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ReunionListComponent_div_10_option_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 32);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const planning_r11 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", planning_r11.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(planning_r11.titre);\n  }\n}\nconst _c0 = function (a0) {\n  return {\n    \"animate__animated animate__fadeInDown\": a0\n  };\n};\nfunction ReunionListComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"div\", 16)(2, \"div\", 17)(3, \"div\", 4)(4, \"div\", 18)(5, \"div\", 19);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(6, \"svg\", 20);\n    i0.ɵɵelement(7, \"path\", 7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(8, \"input\", 21);\n    i0.ɵɵlistener(\"ngModelChange\", function ReunionListComponent_div_10_Template_input_ngModelChange_8_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.searchTerm = $event);\n    })(\"input\", function ReunionListComponent_div_10_Template_input_input_8_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.searchReunions());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, ReunionListComponent_div_10_button_9_Template, 3, 0, \"button\", 22);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 23)(11, \"div\", 4)(12, \"div\", 18)(13, \"select\", 24);\n    i0.ɵɵlistener(\"ngModelChange\", function ReunionListComponent_div_10_Template_select_ngModelChange_13_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.selectedPlanning = $event);\n    })(\"change\", function ReunionListComponent_div_10_Template_select_change_13_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.searchReunions());\n    });\n    i0.ɵɵelementStart(14, \"option\", 25);\n    i0.ɵɵtext(15, \"Tous les plannings\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(16, ReunionListComponent_div_10_option_16_Template, 2, 2, \"option\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 27);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(18, \"svg\", 28);\n    i0.ɵɵelement(19, \"path\", 29);\n    i0.ɵɵelementEnd()()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(5, _c0, ctx_r0.showSearchBar));\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.searchTerm);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.searchTerm);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.selectedPlanning);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.uniquePlannings);\n  }\n}\nfunction ReunionListComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 34);\n    i0.ɵɵelement(2, \"path\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Les r\\u00E9unions \\u00E0 \");\n    i0.ɵɵelementStart(5, \"span\", 36);\n    i0.ɵɵtext(6, \"pr\\u00E9sence obligatoire\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(7, \" sont affich\\u00E9es en premier\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ReunionListComponent_div_12_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 40);\n    i0.ɵɵtext(1, \" Aucune r\\u00E9union ne correspond \\u00E0 votre recherche. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReunionListComponent_div_12_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r18.filteredReunions.length, \" r\\u00E9union(s) trouv\\u00E9e(s) \");\n  }\n}\nfunction ReunionListComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 37);\n    i0.ɵɵtemplate(1, ReunionListComponent_div_12_span_1_Template, 2, 0, \"span\", 38);\n    i0.ɵɵtemplate(2, ReunionListComponent_div_12_span_2_Template, 2, 1, \"span\", 39);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.filteredReunions.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.filteredReunions.length > 0);\n  }\n}\nfunction ReunionListComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 41);\n    i0.ɵɵelement(1, \"div\", 42);\n    i0.ɵɵelementStart(2, \"p\", 43);\n    i0.ɵɵtext(3, \"Chargement de vos r\\u00E9unions...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ReunionListComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 44)(1, \"div\", 18);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 45);\n    i0.ɵɵelement(3, \"path\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"Erreur lors du chargement des r\\u00E9unions: \", ctx_r4.error.message, \"\");\n  }\n}\nfunction ReunionListComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 47)(1, \"div\", 48);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 49);\n    i0.ɵɵelement(3, \"path\", 50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(4, \"h3\", 51);\n    i0.ɵɵtext(5, \"Aucune r\\u00E9union pr\\u00E9vue\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 52);\n    i0.ɵɵtext(7, \"Vous pouvez cr\\u00E9er des r\\u00E9unions depuis la page d\\u00E9tail d'un planning.\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"animated\", !ctx_r5.loading);\n  }\n}\nfunction ReunionListComponent_div_16_div_1_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 82);\n    i0.ɵɵtext(1, \" Pr\\u00E9sence Obligatoire \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReunionListComponent_div_16_div_1_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 83)(1, \"div\", 18);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 69);\n    i0.ɵɵelement(3, \"path\", 84);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(4, \"strong\");\n    i0.ɵɵtext(5, \"Participants:\\u00A0\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const reunion_r20 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\"\", reunion_r20.participants.length, \" \");\n  }\n}\nfunction ReunionListComponent_div_16_div_1_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 85)(1, \"a\", 86);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 75);\n    i0.ɵɵelement(3, \"path\", 87);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Rejoindre la visioconf\\u00E9rence \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const reunion_r20 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate(\"href\", reunion_r20.lienVisio, i0.ɵɵsanitizeUrl);\n  }\n}\nconst _c1 = function (a1) {\n  return [\"/reunions/reunionDetails\", a1];\n};\nfunction ReunionListComponent_div_16_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 55)(1, \"div\", 56)(2, \"div\", 17)(3, \"div\", 18)(4, \"h3\", 57)(5, \"a\", 58);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(7, ReunionListComponent_div_16_div_1_span_7_Template, 2, 0, \"span\", 59);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"p\", 60);\n    i0.ɵɵpipe(9, \"highlightPresence\");\n    i0.ɵɵelementStart(10, \"div\", 61);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(11, \"svg\", 62);\n    i0.ɵɵelement(12, \"path\", 63);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(13, \"span\");\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"div\", 64)(17, \"span\");\n    i0.ɵɵtext(18);\n    i0.ɵɵpipe(19, \"titlecase\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"button\", 65);\n    i0.ɵɵlistener(\"click\", function ReunionListComponent_div_16_div_1_Template_button_click_20_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r28);\n      const reunion_r20 = restoredCtx.$implicit;\n      const ctx_r27 = i0.ɵɵnextContext(2);\n      ctx_r27.deleteReunion(reunion_r20._id || reunion_r20.id);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(21, \"svg\", 66);\n    i0.ɵɵelement(22, \"path\", 67);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(23, \"div\", 68);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(24, \"svg\", 69);\n    i0.ɵɵelement(25, \"path\", 70);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(26, \"span\")(27, \"strong\");\n    i0.ɵɵtext(28, \"Cr\\u00E9ateur:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(30, ReunionListComponent_div_16_div_1_div_30_Template, 7, 1, \"div\", 71);\n    i0.ɵɵelementStart(31, \"div\", 68);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(32, \"svg\", 69);\n    i0.ɵɵelement(33, \"path\", 50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(34, \"span\")(35, \"strong\");\n    i0.ɵɵtext(36, \"Planning:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(37);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(38, ReunionListComponent_div_16_div_1_div_38_Template, 5, 1, \"div\", 72);\n    i0.ɵɵelementStart(39, \"div\", 73)(40, \"div\", 74);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(41, \"svg\", 75);\n    i0.ɵɵelement(42, \"path\", 76)(43, \"path\", 77);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(45, \"div\", 78)(46, \"a\", 79);\n    i0.ɵɵlistener(\"click\", function ReunionListComponent_div_16_div_1_Template_a_click_46_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r28);\n      const reunion_r20 = restoredCtx.$implicit;\n      const ctx_r29 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r29.editReunion(reunion_r20._id || reunion_r20.id));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(47, \"svg\", 80);\n    i0.ɵɵelement(48, \"path\", 81);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(49, \" Modifier \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const reunion_r20 = ctx.$implicit;\n    const i_r21 = ctx.index;\n    const ctx_r19 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"animation-delay\", i_r21 * 100 + \"ms\");\n    i0.ɵɵclassProp(\"animated\", ctx_r19.animateItems)(\"border-l-4\", ctx_r19.hasPresenceObligatoire(reunion_r20))(\"border-red-500\", ctx_r19.hasPresenceObligatoire(reunion_r20));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(30, _c1, reunion_r20._id));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(reunion_r20.titre);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r19.hasPresenceObligatoire(reunion_r20));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"innerHTML\", i0.ɵɵpipeBind1(9, 23, reunion_r20.description), i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate3(\"\", i0.ɵɵpipeBind2(15, 25, reunion_r20.date, \"mediumDate\"), \" \\u2022 \", reunion_r20.heureDebut, \" - \", reunion_r20.heureFin, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMap(\"px-3 py-1 text-xs rounded-full font-medium \" + ctx_r19.getStatutClass(reunion_r20.statut));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(19, 28, reunion_r20.statut), \" \");\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate1(\" \", reunion_r20.createur.username, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", reunion_r20.participants.length > 0);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", reunion_r20.planning.titre, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", reunion_r20.lienVisio);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", reunion_r20.lieu || \"Lieu non sp\\u00E9cifi\\u00E9\", \" \");\n  }\n}\nfunction ReunionListComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵtemplate(1, ReunionListComponent_div_16_div_1_Template, 50, 32, \"div\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.searchTerm || ctx_r6.selectedPlanning ? ctx_r6.filteredReunions : ctx_r6.reunions);\n  }\n}\nexport let ReunionListComponent = /*#__PURE__*/(() => {\n  class ReunionListComponent {\n    // Propriété pour le titre de la page\n    get pageTitle() {\n      return this.authService.getCurrentUserRole() === 'admin' ? 'Toutes les Réunions' : 'Mes Réunions';\n    }\n    constructor(reunionService, router, authService, sanitizer, toastService) {\n      this.reunionService = reunionService;\n      this.router = router;\n      this.authService = authService;\n      this.sanitizer = sanitizer;\n      this.toastService = toastService;\n      this.reunions = [];\n      this.filteredReunions = [];\n      this.loading = true;\n      this.animateItems = false; // Contrôle l'animation des éléments de la liste\n      // Propriétés pour la recherche\n      this.showSearchBar = false;\n      this.searchTerm = '';\n      this.selectedPlanning = '';\n      this.uniquePlannings = [];\n    }\n    ngOnInit() {\n      this.loadReunions();\n      // Test du service de toast\n      console.log('🧪 Test du service de toast...');\n      // this.toastService.success('Test', 'Le service de toast fonctionne !');\n    }\n\n    ngAfterViewInit() {\n      // Activer les animations après un court délai pour permettre le rendu initial\n      setTimeout(() => {\n        this.animateItems = true;\n      }, 100);\n    }\n    /**\n     * Affiche ou masque la barre de recherche\n     */\n    toggleSearchBar() {\n      this.showSearchBar = !this.showSearchBar;\n      // Si on ferme la barre de recherche, réinitialiser les filtres\n      if (!this.showSearchBar) {\n        this.clearSearch();\n      }\n    }\n    /**\n     * Réinitialise les critères de recherche\n     */\n    clearSearch() {\n      this.searchTerm = '';\n      this.selectedPlanning = '';\n      this.searchReunions();\n    }\n    /**\n     * Filtre les réunions selon les critères de recherche\n     */\n    searchReunions() {\n      if (!this.searchTerm && !this.selectedPlanning) {\n        // Si aucun critère de recherche, afficher toutes les réunions\n        this.filteredReunions = [...this.reunions];\n        return;\n      }\n      // Filtrer les réunions selon les critères\n      this.filteredReunions = this.reunions.filter(reunion => {\n        // Vérifier le titre et la description si searchTerm est défini\n        const matchesSearchTerm = !this.searchTerm || reunion.titre && reunion.titre.toLowerCase().includes(this.searchTerm.toLowerCase()) || reunion.description && reunion.description.toLowerCase().includes(this.searchTerm.toLowerCase());\n        // Vérifier le planning si selectedPlanning est défini\n        const matchesPlanning = !this.selectedPlanning || reunion.planning && reunion.planning._id === this.selectedPlanning;\n        // La réunion doit correspondre aux deux critères (si définis)\n        return matchesSearchTerm && matchesPlanning;\n      });\n    }\n    loadReunions() {\n      this.loading = true;\n      this.animateItems = false; // Réinitialiser l'animation\n      const userId = this.authService.getCurrentUserId();\n      const userRole = this.authService.getCurrentUserRole();\n      if (!userId) {\n        this.error = \"Utilisateur non connecté\";\n        this.loading = false;\n        return;\n      }\n      // Si l'utilisateur est admin, récupérer toutes les réunions\n      // Sinon, récupérer seulement ses réunions\n      const reunionObservable = userRole === 'admin' ? this.reunionService.getAllReunionsAdmin() : this.reunionService.getProchainesReunions(userId);\n      reunionObservable.subscribe({\n        next: response => {\n          console.log('Réunions chargées:', response);\n          // Réinitialiser les erreurs\n          this.error = null;\n          // Attribuer les données après un court délai pour l'animation\n          setTimeout(() => {\n            // Récupérer les réunions selon la structure de réponse\n            let reunions = userRole === 'admin' ? response.data || response.reunions || [] : response.reunions || [];\n            console.log('Réunions récupérées pour admin:', reunions);\n            console.log('Structure de la première réunion:', reunions[0]);\n            // Pour le test : ajouter \"présence obligatoire\" à certaines réunions si aucune n'en a\n            reunions = this.ajouterPresenceObligatoirePourTest(reunions);\n            // Trier les réunions : celles avec \"Présence Obligatoire\" en premier\n            this.reunions = this.trierReunionsParPresenceObligatoire(reunions);\n            // Initialiser les réunions filtrées avec toutes les réunions\n            this.filteredReunions = [...this.reunions];\n            // Extraire les plannings uniques pour le filtre\n            this.extractUniquePlannings();\n            this.loading = false;\n            // Activer les animations après un court délai\n            setTimeout(() => {\n              this.animateItems = true;\n            }, 100);\n          }, 300); // Délai pour une meilleure expérience visuelle\n        },\n\n        error: error => {\n          console.error('Erreur détaillée:', JSON.stringify(error));\n          this.error = `Erreur lors du chargement des réunions: ${error.message || error.statusText || 'Erreur inconnue'}`;\n          this.loading = false;\n        }\n      });\n    }\n    getStatutClass(statut) {\n      switch (statut) {\n        case 'planifiee':\n          return 'bg-blue-100 text-blue-800';\n        case 'en_cours':\n          return 'bg-yellow-100 text-yellow-800';\n        case 'terminee':\n          return 'bg-green-100 text-green-800';\n        case 'annulee':\n          return 'bg-red-100 text-red-800';\n        default:\n          return 'bg-gray-100 text-gray-800';\n      }\n    }\n    editReunion(id) {\n      console.log(id);\n      if (this.reunions) {\n        this.router.navigate(['/reunions/modifier', id]);\n      }\n    }\n    /**\n     * Supprime une réunion après confirmation\n     * @param id ID de la réunion à supprimer\n     */\n    deleteReunion(id) {\n      console.log('🗑️ Tentative de suppression de la réunion avec ID:', id);\n      if (confirm('Êtes-vous sûr de vouloir supprimer cette réunion ?')) {\n        const userRole = this.authService.getCurrentUserRole();\n        console.log('👤 Rôle utilisateur:', userRole);\n        // Utiliser la méthode appropriée selon le rôle\n        const deleteObservable = userRole === 'admin' ? this.reunionService.forceDeleteReunion(id) : this.reunionService.deleteReunion(id);\n        console.log('🚀 Envoi de la requête de suppression...');\n        deleteObservable.subscribe({\n          next: response => {\n            console.log('✅ Réunion supprimée avec succès:', response);\n            this.handleSuccessfulDeletion(id);\n          },\n          error: error => {\n            console.error('❌ Erreur lors de la suppression:', error);\n            console.error('📋 Détails de l\\'erreur:', {\n              status: error.status,\n              statusText: error.statusText,\n              message: error.error?.message,\n              fullError: error\n            });\n            // Si c'est une erreur 200 mal interprétée ou une erreur de CORS,\n            // on considère que la suppression a réussi\n            if (error.status === 0 || error.status === 200) {\n              console.log('🔄 Erreur probablement liée à CORS ou réponse mal formatée, on considère la suppression comme réussie');\n              this.handleSuccessfulDeletion(id);\n              return;\n            }\n            // Pour les autres erreurs, on vérifie quand même si la suppression a eu lieu\n            // en rechargeant la liste après un délai\n            if (error.status >= 500) {\n              console.log('🔄 Erreur serveur, vérification de la suppression dans 2 secondes...');\n              setTimeout(() => {\n                this.loadReunions();\n              }, 2000);\n            }\n            // Gestion spécifique des erreurs d'autorisation\n            if (error.status === 403) {\n              this.toastService.accessDenied('supprimer cette réunion', error.status);\n            } else if (error.status === 401) {\n              this.toastService.error('Non autorisé', 'Vous devez être connecté pour supprimer une réunion');\n            } else {\n              const errorMessage = error.error?.message || 'Erreur lors de la suppression de la réunion';\n              this.toastService.error('Erreur de suppression', errorMessage, 8000);\n            }\n          }\n        });\n      } else {\n        console.log('❌ Suppression annulée par l\\'utilisateur');\n      }\n    }\n    handleSuccessfulDeletion(id) {\n      console.log('🎯 Traitement de la suppression réussie pour l\\'ID:', id);\n      // Retirer la réunion de la liste locale (gérer _id et id)\n      const initialCount = this.reunions.length;\n      this.reunions = this.reunions.filter(reunion => reunion._id !== id && reunion.id !== id);\n      this.filteredReunions = this.filteredReunions.filter(reunion => reunion._id !== id && reunion.id !== id);\n      const finalCount = this.reunions.length;\n      console.log(`📊 Réunions avant suppression: ${initialCount}, après: ${finalCount}`);\n      // Mettre à jour la liste des plannings uniques\n      this.extractUniquePlannings();\n      // Afficher le toast de succès\n      this.toastService.success('Réunion supprimée', 'La réunion a été supprimée avec succès');\n      console.log('🎉 Toast de succès affiché et liste mise à jour');\n      // Recharger complètement la liste pour s'assurer de la mise à jour\n      this.loadReunions();\n    }\n    formatDescription(description) {\n      if (!description) return this.sanitizer.bypassSecurityTrustHtml('');\n      // Recherche la chaîne \"(presence obligatoire)\" (insensible à la casse) et la remplace par une version en rouge\n      const formattedText = description.replace(/\\(presence obligatoire\\)/gi, '<span class=\"text-red-600 font-semibold\">(presence obligatoire)</span>');\n      // Sanitize le HTML pour éviter les problèmes de sécurité\n      return this.sanitizer.bypassSecurityTrustHtml(formattedText);\n    }\n    /**\n     * Vérifie si une réunion contient \"Présence Obligatoire\" dans sa description\n     * @param reunion La réunion à vérifier\n     * @returns true si la réunion a une présence obligatoire, false sinon\n     */\n    hasPresenceObligatoire(reunion) {\n      if (!reunion.description) return false;\n      // Recherche différentes variations de \"présence obligatoire\" (insensible à la casse)\n      const patterns = [/presence obligatoire/i, /présence obligatoire/i, /obligatoire/i, /\\(obligatoire\\)/i, /\\(presence obligatoire\\)/i, /\\(présence obligatoire\\)/i];\n      // Retourne true si l'une des expressions est trouvée\n      return patterns.some(pattern => pattern.test(reunion.description));\n    }\n    /**\n     * Trie les réunions en mettant celles avec \"Présence Obligatoire\" en premier\n     * @param reunions Liste des réunions à trier\n     * @returns Liste triée des réunions\n     */\n    trierReunionsParPresenceObligatoire(reunions) {\n      if (!reunions || !reunions.length) return [];\n      console.log('Avant tri - Nombre de réunions:', reunions.length);\n      // Vérifier chaque réunion pour la présence obligatoire\n      reunions.forEach((reunion, index) => {\n        const hasPresence = this.hasPresenceObligatoire(reunion);\n        console.log(`Réunion ${index + 1} - Titre: ${reunion.titre}, Description: ${reunion.description}, Présence Obligatoire: ${hasPresence}`);\n      });\n      // Trier les réunions : celles avec \"Présence Obligatoire\" en premier\n      const reunionsTriees = [...reunions].sort((a, b) => {\n        const aHasPresenceObligatoire = this.hasPresenceObligatoire(a);\n        const bHasPresenceObligatoire = this.hasPresenceObligatoire(b);\n        if (aHasPresenceObligatoire && !bHasPresenceObligatoire) {\n          return -1; // a vient avant b\n        }\n\n        if (!aHasPresenceObligatoire && bHasPresenceObligatoire) {\n          return 1; // b vient avant a\n        }\n        // Si les deux ont ou n'ont pas \"Présence Obligatoire\", trier par date\n        return new Date(b.date).getTime() - new Date(a.date).getTime();\n      });\n      console.log('Après tri - Ordre des réunions:');\n      reunionsTriees.forEach((reunion, index) => {\n        const hasPresence = this.hasPresenceObligatoire(reunion);\n        console.log(`Position ${index + 1} - Titre: ${reunion.titre}, Présence Obligatoire: ${hasPresence}`);\n      });\n      return reunionsTriees;\n    }\n    /**\n     * Méthode temporaire pour ajouter \"présence obligatoire\" à certaines réunions pour tester le tri\n     * @param reunions Liste des réunions\n     * @returns Liste des réunions avec certaines marquées comme \"présence obligatoire\"\n     */\n    /**\n     * Extrait les plannings uniques à partir des réunions pour le filtre\n     */\n    extractUniquePlannings() {\n      // Map pour stocker les plannings uniques par ID\n      const planningsMap = new Map();\n      // Parcourir toutes les réunions\n      this.reunions.forEach(reunion => {\n        if (reunion.planning && reunion.planning._id) {\n          // Ajouter le planning au Map s'il n'existe pas déjà\n          if (!planningsMap.has(reunion.planning._id)) {\n            planningsMap.set(reunion.planning._id, {\n              id: reunion.planning._id,\n              titre: reunion.planning.titre\n            });\n          }\n        }\n      });\n      // Convertir le Map en tableau\n      this.uniquePlannings = Array.from(planningsMap.values());\n      // Trier les plannings par titre\n      this.uniquePlannings.sort((a, b) => a.titre.localeCompare(b.titre));\n    }\n    /**\n     * Méthode temporaire pour ajouter \"présence obligatoire\" à certaines réunions pour tester le tri\n     */\n    ajouterPresenceObligatoirePourTest(reunions) {\n      if (!reunions || reunions.length === 0) return reunions;\n      // Vérifier si au moins une réunion a déjà \"présence obligatoire\"\n      const hasAnyPresenceObligatoire = reunions.some(reunion => this.hasPresenceObligatoire(reunion));\n      // Si aucune réunion n'a \"présence obligatoire\", en ajouter à certaines pour le test\n      if (!hasAnyPresenceObligatoire) {\n        console.log('Aucune réunion avec présence obligatoire trouvée, ajout pour le test...');\n        // Ajouter \"présence obligatoire\" à la première réunion si elle existe\n        if (reunions.length > 0) {\n          const reunion = reunions[0];\n          reunion.description = reunion.description ? reunion.description + ' (présence obligatoire)' : '(présence obligatoire)';\n          console.log(`Ajout de \"présence obligatoire\" à la réunion: ${reunion.titre}`);\n        }\n        // Si au moins 3 réunions, ajouter aussi à la troisième\n        if (reunions.length >= 3) {\n          const reunion = reunions[2];\n          reunion.description = reunion.description ? reunion.description + ' (présence obligatoire)' : '(présence obligatoire)';\n          console.log(`Ajout de \"présence obligatoire\" à la réunion: ${reunion.titre}`);\n        }\n      }\n      return reunions;\n    }\n    static {\n      this.ɵfac = function ReunionListComponent_Factory(t) {\n        return new (t || ReunionListComponent)(i0.ɵɵdirectiveInject(i1.ReunionService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.AuthuserService), i0.ɵɵdirectiveInject(i4.DomSanitizer), i0.ɵɵdirectiveInject(i5.ToastService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ReunionListComponent,\n        selectors: [[\"app-reunion-list\"]],\n        decls: 17,\n        vars: 8,\n        consts: [[1, \"container\", \"mx-auto\", \"px-4\", \"py-6\", \"page-container\", \"page-enter\"], [1, \"flex\", \"flex-col\", \"mb-8\"], [1, \"flex\", \"justify-between\", \"items-center\"], [1, \"text-2xl\", \"font-bold\", \"text-gray-800\", \"page-title\"], [1, \"relative\"], [1, \"search-button\", \"px-4\", \"py-2\", \"bg-purple-200\", \"text-purple-800\", \"rounded-md\", \"hover:bg-purple-300\", \"transition-colors\", \"transform\", \"hover:scale-105\", \"duration-200\", \"flex\", \"items-center\", \"shadow-sm\", \"border\", \"border-purple-300\", 3, \"click\"], [\"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-5\", \"w-5\", \"mr-2\", \"text-purple-600\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"], [\"class\", \"mt-4 bg-white p-4 rounded-lg shadow-md transition-all duration-300 animate-fadeIn\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"mt-2 text-sm text-gray-600 flex items-center\", 4, \"ngIf\"], [\"class\", \"mt-2 text-sm text-gray-600\", 4, \"ngIf\"], [\"class\", \"text-center py-12\", 4, \"ngIf\"], [\"class\", \"bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded-md mb-6 shadow-md transform transition-all duration-500 hover:shadow-lg\", 4, \"ngIf\"], [\"class\", \"text-center py-12 empty-container\", 3, \"animated\", 4, \"ngIf\"], [\"class\", \"grid grid-cols-1 md:grid-cols-2 gap-6\", 4, \"ngIf\"], [1, \"mt-4\", \"bg-white\", \"p-4\", \"rounded-lg\", \"shadow-md\", \"transition-all\", \"duration-300\", \"animate-fadeIn\", 3, \"ngClass\"], [1, \"flex\", \"flex-col\", \"md:flex-row\", \"gap-4\"], [1, \"flex-1\"], [1, \"flex\", \"items-center\"], [1, \"absolute\", \"left-3\", \"top-1/2\", \"transform\", \"-translate-y-1/2\", \"text-purple-400\"], [\"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-5\", \"w-5\"], [\"type\", \"text\", \"id\", \"searchTerm\", \"placeholder\", \"Rechercher par titre ou description\", 1, \"w-full\", \"pl-10\", \"pr-10\", \"py-3\", \"border\", \"border-gray-300\", \"rounded-md\", \"focus:ring-2\", \"focus:ring-purple-300\", \"focus:border-purple-400\", \"transition-all\", \"duration-300\", 3, \"ngModel\", \"ngModelChange\", \"input\"], [\"class\", \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-purple-600 transition-colors\", 3, \"click\", 4, \"ngIf\"], [1, \"md:w-1/3\"], [\"id\", \"planningFilter\", 1, \"w-full\", \"px-4\", \"py-3\", \"border\", \"border-gray-300\", \"rounded-md\", \"focus:ring-2\", \"focus:ring-purple-300\", \"focus:border-purple-400\", \"transition-all\", \"duration-300\", \"appearance-none\", 3, \"ngModel\", \"ngModelChange\", \"change\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"absolute\", \"inset-y-0\", \"right-0\", \"flex\", \"items-center\", \"pr-3\", \"pointer-events-none\"], [\"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-5\", \"w-5\", \"text-purple-500\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M19 9l-7 7-7-7\"], [1, \"absolute\", \"right-3\", \"top-1/2\", \"transform\", \"-translate-y-1/2\", \"text-gray-400\", \"hover:text-purple-600\", \"transition-colors\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M6 18L18 6M6 6l12 12\"], [3, \"value\"], [1, \"mt-2\", \"text-sm\", \"text-gray-600\", \"flex\", \"items-center\"], [\"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-4\", \"w-4\", \"mr-2\", \"text-red-500\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M13 10V3L4 14h7v7l9-11h-7z\"], [1, \"font-semibold\", \"text-red-600\"], [1, \"mt-2\", \"text-sm\", \"text-gray-600\"], [\"class\", \"text-red-500\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"text-red-500\"], [1, \"text-center\", \"py-12\"], [1, \"loading-spinner\", \"rounded-full\", \"h-16\", \"w-16\", \"border-4\", \"border-purple-200\", \"border-t-purple-600\", \"mx-auto\"], [1, \"mt-4\", \"text-gray-600\", \"animate-pulse\"], [1, \"bg-red-100\", \"border-l-4\", \"border-red-500\", \"text-red-700\", \"p-4\", \"rounded-md\", \"mb-6\", \"shadow-md\", \"transform\", \"transition-all\", \"duration-500\", \"hover:shadow-lg\"], [\"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-6\", \"w-6\", \"mr-3\", \"text-red-500\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"text-center\", \"py-12\", \"empty-container\"], [1, \"bg-white\", \"rounded-lg\", \"shadow-md\", \"p-8\", \"max-w-md\", \"mx-auto\"], [\"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"mx-auto\", \"h-16\", \"w-16\", \"text-purple-400\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"], [1, \"mt-4\", \"text-xl\", \"font-medium\", \"text-gray-900\"], [1, \"mt-2\", \"text-gray-500\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"gap-6\"], [\"class\", \"bg-white rounded-lg shadow-md p-5 hover:shadow-xl transition-all duration-300 reunion-card\", 3, \"animated\", \"border-l-4\", \"border-red-500\", \"animation-delay\", 4, \"ngFor\", \"ngForOf\"], [1, \"bg-white\", \"rounded-lg\", \"shadow-md\", \"p-5\", \"hover:shadow-xl\", \"transition-all\", \"duration-300\", \"reunion-card\"], [1, \"flex\", \"justify-between\", \"items-start\"], [1, \"text-lg\", \"font-semibold\", \"text-gray-800\", \"hover:text-purple-600\", \"transition-colors\"], [1, \"hover:text-purple-600\", 3, \"routerLink\"], [\"class\", \"ml-2 px-2 py-1 text-xs bg-red-100 text-red-800 rounded-full font-bold animate-pulse\", 4, \"ngIf\"], [1, \"text-sm\", \"mt-1\", 3, \"innerHTML\"], [1, \"mt-3\", \"flex\", \"items-center\", \"text-sm\", \"text-gray-500\"], [\"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-4\", \"w-4\", \"mr-2\", \"text-purple-500\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"flex\", \"items-start\", \"space-x-2\"], [\"title\", \"Supprimer la r\\u00E9union\", 1, \"text-red-500\", \"hover:text-red-700\", \"transition-colors\", \"duration-300\", \"p-1\", \"rounded-full\", \"hover:bg-red-50\", 3, \"click\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-5\", \"w-5\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"], [1, \"mt-3\", \"text-sm\", \"text-gray-600\", \"flex\", \"items-center\"], [\"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-4\", \"w-4\", \"mr-2\", \"text-gray-500\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"], [\"class\", \"mt-3 text-sm text-gray-600\", 4, \"ngIf\"], [\"class\", \"mt-3 text-sm\", 4, \"ngIf\"], [1, \"mt-4\", \"pt-3\", \"border-t\", \"border-gray-100\", \"flex\", \"justify-between\", \"items-center\"], [1, \"flex\", \"items-center\", \"text-sm\", \"text-gray-500\"], [\"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-4\", \"w-4\", \"mr-2\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M15 11a3 3 0 11-6 0 3 3 0 016 0z\"], [1, \"flex\", \"items-center\", \"space-x-2\"], [1, \"px-4\", \"py-2\", \"bg-purple-600\", \"text-white\", \"rounded-md\", \"hover:bg-purple-700\", \"transition-all\", \"duration-300\", \"transform\", \"hover:scale-105\", \"flex\", \"items-center\", 3, \"click\"], [\"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-4\", \"w-4\", \"mr-1\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"], [1, \"ml-2\", \"px-2\", \"py-1\", \"text-xs\", \"bg-red-100\", \"text-red-800\", \"rounded-full\", \"font-bold\", \"animate-pulse\"], [1, \"mt-3\", \"text-sm\", \"text-gray-600\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"], [1, \"mt-3\", \"text-sm\"], [\"target\", \"_blank\", 1, \"text-purple-600\", \"hover:text-purple-800\", \"flex\", \"items-center\", \"transition-colors\", 3, \"href\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z\"]],\n        template: function ReunionListComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\", 3);\n            i0.ɵɵtext(4);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"div\", 4)(6, \"button\", 5);\n            i0.ɵɵlistener(\"click\", function ReunionListComponent_Template_button_click_6_listener() {\n              return ctx.toggleSearchBar();\n            });\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(7, \"svg\", 6);\n            i0.ɵɵelement(8, \"path\", 7);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(9, \" Rechercher \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(10, ReunionListComponent_div_10_Template, 20, 7, \"div\", 8);\n            i0.ɵɵtemplate(11, ReunionListComponent_div_11_Template, 8, 0, \"div\", 9);\n            i0.ɵɵtemplate(12, ReunionListComponent_div_12_Template, 3, 2, \"div\", 10);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(13, ReunionListComponent_div_13_Template, 4, 0, \"div\", 11);\n            i0.ɵɵtemplate(14, ReunionListComponent_div_14_Template, 6, 1, \"div\", 12);\n            i0.ɵɵtemplate(15, ReunionListComponent_div_15_Template, 8, 2, \"div\", 13);\n            i0.ɵɵtemplate(16, ReunionListComponent_div_16_Template, 2, 1, \"div\", 14);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(4);\n            i0.ɵɵtextInterpolate(ctx.pageTitle);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"ngIf\", ctx.showSearchBar);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.reunions.length > 0);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.searchTerm || ctx.selectedPlanning);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.error);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.reunions.length === 0);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.reunions.length > 0);\n          }\n        },\n        dependencies: [i6.NgClass, i6.NgForOf, i6.NgIf, i2.RouterLink, i7.NgSelectOption, i7.ɵNgSelectMultipleOption, i7.DefaultValueAccessor, i7.SelectControlValueAccessor, i7.NgControlStatus, i7.NgModel, i6.TitleCasePipe, i6.DatePipe, i8.HighlightPresencePipe],\n        styles: [\".page-container[_ngcontent-%COMP%]{overflow:hidden}.page-title[_ngcontent-%COMP%]{position:relative;display:inline-block}.page-title[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;width:0;height:3px;bottom:-5px;left:0;background-color:#8b5cf6;transition:width .6s ease}.page-title[_ngcontent-%COMP%]:hover:after{width:100%}.reunion-card[_ngcontent-%COMP%]{transform:translateY(30px);opacity:0;transition:all .5s cubic-bezier(.4,0,.2,1)}.reunion-card.animated[_ngcontent-%COMP%]{transform:translateY(0);opacity:1}.empty-container[_ngcontent-%COMP%]{transform:scale(.8);opacity:0;transition:all .6s cubic-bezier(.34,1.56,.64,1)}.empty-container.animated[_ngcontent-%COMP%]{transform:scale(1);opacity:1}.loading-spinner[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_pulse 1.5s infinite ease-in-out}@keyframes _ngcontent-%COMP%_pulse{0%{transform:scale(.95);box-shadow:0 0 #8b5cf6b3}70%{transform:scale(1);box-shadow:0 0 0 10px #8b5cf600}to{transform:scale(.95);box-shadow:0 0 #8b5cf600}}.page-enter[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeInUp .8s forwards}@keyframes _ngcontent-%COMP%_fadeInUp{0%{opacity:0;transform:translateY(40px)}to{opacity:1;transform:translateY(0)}}.animate-fadeIn[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeIn .3s ease-in-out}@keyframes _ngcontent-%COMP%_slideInFromRight{0%{transform:translate(30px);opacity:0}to{transform:translate(0);opacity:1}}.flex-col[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(1){animation:_ngcontent-%COMP%_slideInFromRight .4s ease-out forwards}.flex-col[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(2){animation:_ngcontent-%COMP%_slideInFromRight .4s ease-out .1s forwards;opacity:0}input[_ngcontent-%COMP%], select[_ngcontent-%COMP%]{transition:all .3s ease}input[_ngcontent-%COMP%]:focus, select[_ngcontent-%COMP%]:focus{transform:translateY(-2px);box-shadow:0 4px 6px -1px #8b5cf61a,0 2px 4px -1px #8b5cf60f}@keyframes _ngcontent-%COMP%_gentle-pulse{0%{box-shadow:0 0 #a78bfa66}70%{box-shadow:0 0 0 6px #a78bfa00}to{box-shadow:0 0 #a78bfa00}}.search-button[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_gentle-pulse 2s infinite}@keyframes _ngcontent-%COMP%_fadeIn{0%{opacity:0;transform:translateY(-10px)}to{opacity:1;transform:translateY(0)}}.search-input-container[_ngcontent-%COMP%]{position:relative;overflow:hidden;transition:all .3s ease}.search-input-container[_ngcontent-%COMP%]:hover{transform:translateY(-2px)}.search-input[_ngcontent-%COMP%]{position:relative;z-index:1;background:transparent;transition:all .3s ease}.search-input[_ngcontent-%COMP%]:focus{box-shadow:0 0 0 3px #8b5cf64d}.search-input-container[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(139,92,246,.1),transparent);transition:all .6s ease;z-index:0}.search-input-container[_ngcontent-%COMP%]:hover:before{left:100%;transition:all .6s ease}@keyframes _ngcontent-%COMP%_subtlePulse{0%{box-shadow:0 0 #8b5cf633}50%{box-shadow:0 0 0 5px #8b5cf600}to{box-shadow:0 0 #8b5cf600}}.search-input-container[_ngcontent-%COMP%]:focus-within{animation:_ngcontent-%COMP%_subtlePulse 2s infinite;border-color:#8b5cf6}@keyframes _ngcontent-%COMP%_rotateIcon{0%{transform:rotate(0)}25%{transform:rotate(-10deg)}75%{transform:rotate(10deg)}to{transform:rotate(0)}}.search-icon[_ngcontent-%COMP%]{transition:all .3s ease}.search-input-container[_ngcontent-%COMP%]:focus-within   .search-icon[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_rotateIcon 1s ease;color:#8b5cf6}.search-input[_ngcontent-%COMP%]::placeholder{transition:all .3s ease}.search-input[_ngcontent-%COMP%]:focus::placeholder{opacity:.5;transform:translate(10px)}.floating-label[_ngcontent-%COMP%]{position:absolute;left:12px;top:50%;transform:translateY(-50%);font-size:14px;color:#9ca3af;pointer-events:none;transition:all .3s ease;z-index:2}.search-input[_ngcontent-%COMP%]:focus ~ .floating-label[_ngcontent-%COMP%], .search-input[_ngcontent-%COMP%]:not(:placeholder-shown) ~ .floating-label[_ngcontent-%COMP%]{top:0;left:8px;font-size:12px;padding:0 4px;background-color:#fff;color:#8b5cf6;transform:translateY(-50%)}.search-select[_ngcontent-%COMP%]{position:relative;transition:all .3s ease;background-image:linear-gradient(to right,#f9fafb 0%,white 100%)}.search-select[_ngcontent-%COMP%]:hover{background-image:linear-gradient(to right,#f3f4f6 0%,white 100%)}.search-select[_ngcontent-%COMP%]:focus{box-shadow:0 0 0 3px #8b5cf64d;background-image:linear-gradient(to right,#f3f4f6 0%,white 100%)}.staggered-item[_ngcontent-%COMP%]{opacity:0;transform:translateY(20px)}.staggered-item.animated[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeInStaggered .5s forwards}@keyframes _ngcontent-%COMP%_fadeInStaggered{to{opacity:1;transform:translateY(0)}}\"],\n        data: {\n          animation: [trigger('fadeIn', [transition(':enter', [style({\n            opacity: 0,\n            transform: 'translateY(20px)'\n          }), animate('0.4s ease-out', style({\n            opacity: 1,\n            transform: 'translateY(0)'\n          }))])]), trigger('staggerList', [transition('* => *', [query(':enter', [style({\n            opacity: 0,\n            transform: 'translateY(30px)'\n          }), stagger('100ms', [animate('0.5s ease-out', style({\n            opacity: 1,\n            transform: 'translateY(0)'\n          }))])], {\n            optional: true\n          })])])]\n        }\n      });\n    }\n  }\n  return ReunionListComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}