{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MessagesRoutingModule } from './messages-routing.module';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { ApolloModule } from 'apollo-angular';\nimport { UserStatusService } from 'src/app/services/user-status.service';\nimport { MessageService } from 'src/app/services/message.service';\nimport * as i0 from \"@angular/core\";\n// import { SharedComponentsModule } from '../../../shared/components/shared-components.module';\nexport let MessagesModule = /*#__PURE__*/(() => {\n  class MessagesModule {\n    static {\n      this.ɵfac = function MessagesModule_Factory(t) {\n        return new (t || MessagesModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: MessagesModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        providers: [UserStatusService, MessageService],\n        imports: [CommonModule, MessagesRoutingModule, FormsModule, ReactiveFormsModule, ApolloModule, RouterModule]\n      });\n    }\n  }\n  return MessagesModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}