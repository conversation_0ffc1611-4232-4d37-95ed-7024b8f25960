/* ============================================================================
   PAGE DE TEST - NOTIFICATIONS ET CHAT
   ============================================================================ */

.test-page-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  color: #ffffff;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  padding: 2rem;
}

/* ============================================================================
   EN-TÊTE
   ============================================================================ */

.test-header {
  text-align: center;
  margin-bottom: 3rem;
  padding: 2rem;
  background: rgba(30, 41, 59, 0.5);
  border-radius: 1rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.test-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  font-size: 2.5rem;
  font-weight: 700;
  color: #ffffff;
  margin: 0 0 1rem 0;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.test-title i {
  color: #3b82f6;
  font-size: 2rem;
}

.test-description {
  font-size: 1.125rem;
  color: #94a3b8;
  margin: 0;
  line-height: 1.6;
}

/* ============================================================================
   SECTIONS
   ============================================================================ */

.test-section {
  margin-bottom: 3rem;
  background: rgba(30, 41, 59, 0.3);
  border-radius: 1rem;
  padding: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.5rem;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 2rem 0;
  padding-bottom: 1rem;
  border-bottom: 2px solid rgba(59, 130, 246, 0.3);
}

.section-title i {
  color: #3b82f6;
  font-size: 1.25rem;
}

/* ============================================================================
   GROUPES DE TESTS
   ============================================================================ */

.test-group {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: rgba(15, 23, 42, 0.5);
  border-radius: 0.75rem;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.group-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #e2e8f0;
  margin: 0 0 1rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.test-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
}

/* ============================================================================
   BOUTONS DE TEST
   ============================================================================ */

.test-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  border: none;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid transparent;
  min-width: 120px;
  justify-content: center;
}

.test-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.test-btn:active {
  transform: translateY(0);
}

/* Types de boutons */
.test-btn.success {
  background: linear-gradient(135deg, #10b981, #059669);
  color: #ffffff;
  border-color: #10b981;
}

.test-btn.success:hover {
  background: linear-gradient(135deg, #059669, #047857);
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
}

.test-btn.error {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: #ffffff;
  border-color: #ef4444;
}

.test-btn.error:hover {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
}

.test-btn.warning {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: #ffffff;
  border-color: #f59e0b;
}

.test-btn.warning:hover {
  background: linear-gradient(135deg, #d97706, #b45309);
  box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);
}

.test-btn.info {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: #ffffff;
  border-color: #3b82f6;
}

.test-btn.info:hover {
  background: linear-gradient(135deg, #2563eb, #1d4ed8);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
}

.test-btn.template {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  color: #ffffff;
  border-color: #8b5cf6;
}

.test-btn.template:hover {
  background: linear-gradient(135deg, #7c3aed, #6d28d9);
  box-shadow: 0 8px 25px rgba(139, 92, 246, 0.4);
}

.test-btn.advanced {
  background: linear-gradient(135deg, #06b6d4, #0891b2);
  color: #ffffff;
  border-color: #06b6d4;
}

.test-btn.advanced:hover {
  background: linear-gradient(135deg, #0891b2, #0e7490);
  box-shadow: 0 8px 25px rgba(6, 182, 212, 0.4);
}

.test-btn.clear {
  background: linear-gradient(135deg, #64748b, #475569);
  color: #ffffff;
  border-color: #64748b;
}

.test-btn.clear:hover {
  background: linear-gradient(135deg, #475569, #334155);
  box-shadow: 0 8px 25px rgba(100, 116, 139, 0.4);
}

.test-btn.setting {
  background: rgba(255, 255, 255, 0.1);
  color: #e2e8f0;
  border-color: rgba(255, 255, 255, 0.2);
}

.test-btn.setting:hover {
  background: rgba(255, 255, 255, 0.2);
  color: #ffffff;
  box-shadow: 0 8px 25px rgba(255, 255, 255, 0.1);
}

/* ============================================================================
   CONTENEUR DE CHAT
   ============================================================================ */

.chat-container {
  height: 600px;
  border-radius: 1rem;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(15, 23, 42, 0.5);
}

/* ============================================================================
   INSTRUCTIONS
   ============================================================================ */

.instructions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.instruction-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: rgba(15, 23, 42, 0.5);
  border-radius: 0.5rem;
  border-left: 3px solid #3b82f6;
}

.instruction-item i {
  flex-shrink: 0;
  width: 1.5rem;
  height: 1.5rem;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: #ffffff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 600;
  margin-top: 0.125rem;
}

.instruction-item div {
  flex: 1;
  line-height: 1.6;
  color: #e2e8f0;
}

.instruction-item strong {
  color: #ffffff;
  font-weight: 600;
}

/* ============================================================================
   RESPONSIVE
   ============================================================================ */

@media (max-width: 768px) {
  .test-page-container {
    padding: 1rem;
  }
  
  .test-header {
    padding: 1.5rem;
    margin-bottom: 2rem;
  }
  
  .test-title {
    font-size: 2rem;
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .test-section {
    padding: 1.5rem;
    margin-bottom: 2rem;
  }
  
  .section-title {
    font-size: 1.25rem;
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .test-group {
    padding: 1rem;
  }
  
  .test-buttons {
    flex-direction: column;
  }
  
  .test-btn {
    width: 100%;
    min-width: auto;
  }
  
  .chat-container {
    height: 500px;
  }
  
  .instruction-item {
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .instruction-item i {
    align-self: flex-start;
  }
}

@media (max-width: 480px) {
  .test-title {
    font-size: 1.75rem;
  }
  
  .test-description {
    font-size: 1rem;
  }
  
  .section-title {
    font-size: 1.125rem;
  }
  
  .chat-container {
    height: 400px;
  }
}
