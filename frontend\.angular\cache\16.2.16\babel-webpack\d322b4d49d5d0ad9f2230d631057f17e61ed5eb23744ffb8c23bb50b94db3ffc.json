{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule, DatePipe } from '@angular/common';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport { HttpClientModule } from '@angular/common/http';\nimport { SharedComponentsModule } from './components/shared-components.module';\n// AiChatbotModule sera importé directement dans AppModule\nexport let SharedModule = class SharedModule {};\nSharedModule = __decorate([NgModule({\n  declarations: [],\n  imports: [CommonModule, ReactiveFormsModule, FormsModule, RouterModule, HttpClientModule, SharedComponentsModule],\n  providers: [DatePipe],\n  exports: [CommonModule, ReactiveFormsModule, FormsModule, RouterModule, SharedComponentsModule]\n})], SharedModule);", "map": {"version": 3, "names": ["NgModule", "CommonModule", "DatePipe", "ReactiveFormsModule", "FormsModule", "RouterModule", "HttpClientModule", "SharedComponentsModule", "SharedModule", "__decorate", "declarations", "imports", "providers", "exports"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\shared\\shared.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule, DatePipe } from '@angular/common';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport { HttpClientModule } from '@angular/common/http';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { SharedComponentsModule } from './components/shared-components.module';\n\n// AiChatbotModule sera importé directement dans AppModule\n\n@NgModule({\n  declarations: [],\n  imports: [\n    CommonModule,\n    ReactiveFormsModule,\n    FormsModule,\n    RouterModule,\n    HttpClientModule,\n    SharedComponentsModule,\n  ],\n  providers: [DatePipe],\n  exports: [\n    CommonModule,\n    ReactiveFormsModule,\n    FormsModule,\n    RouterModule,\n    SharedComponentsModule,\n  ],\n})\nexport class SharedModule {}\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,EAAEC,QAAQ,QAAQ,iBAAiB;AACxD,SAASC,mBAAmB,EAAEC,WAAW,QAAQ,gBAAgB;AACjE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,gBAAgB,QAAQ,sBAAsB;AAEvD,SAASC,sBAAsB,QAAQ,uCAAuC;AAE9E;AAqBO,WAAMC,YAAY,GAAlB,MAAMA,YAAY,GAAG;AAAfA,YAAY,GAAAC,UAAA,EAnBxBT,QAAQ,CAAC;EACRU,YAAY,EAAE,EAAE;EAChBC,OAAO,EAAE,CACPV,YAAY,EACZE,mBAAmB,EACnBC,WAAW,EACXC,YAAY,EACZC,gBAAgB,EAChBC,sBAAsB,CACvB;EACDK,SAAS,EAAE,CAACV,QAAQ,CAAC;EACrBW,OAAO,EAAE,CACPZ,YAAY,EACZE,mBAAmB,EACnBC,WAAW,EACXC,YAAY,EACZE,sBAAsB;CAEzB,CAAC,C,EACWC,YAAY,CAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}