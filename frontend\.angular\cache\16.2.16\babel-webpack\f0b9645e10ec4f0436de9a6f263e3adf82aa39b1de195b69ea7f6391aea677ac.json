{"ast": null, "code": "import { CommonModule, DatePipe } from '@angular/common';\nimport { ReunionsRoutingModule } from './reunions-routing.module';\nimport { RouterModule } from '@angular/router';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { PipesModule } from '../../../pipes/pipes.module';\nimport * as i0 from \"@angular/core\";\nexport let ReunionsModule = /*#__PURE__*/(() => {\n  class ReunionsModule {\n    static {\n      this.ɵfac = function ReunionsModule_Factory(t) {\n        return new (t || ReunionsModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: ReunionsModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        providers: [DatePipe],\n        imports: [CommonModule, ReunionsRoutingModule, RouterModule, FormsModule, ReactiveFormsModule, PipesModule]\n      });\n    }\n  }\n  return ReunionsModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}