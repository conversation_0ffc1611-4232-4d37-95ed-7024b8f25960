<!-- Page de test pour les notifications et le chat -->
<div class="test-page-container">
  
  <!-- En-tête -->
  <div class="test-header">
    <h1 class="test-title">
      <i class="fas fa-flask"></i>
      Test des Notifications et du Chat
    </h1>
    <p class="test-description">
      Cette page permet de tester toutes les fonctionnalités du système de notifications et de chat.
    </p>
  </div>
  
  <!-- Section des notifications -->
  <div class="test-section">
    <h2 class="section-title">
      <i class="fas fa-bell"></i>
      Tests des Notifications
    </h2>
    
    <!-- Tests de base -->
    <div class="test-group">
      <h3 class="group-title">Notifications de base</h3>
      <div class="test-buttons">
        <button (click)="testSuccessNotification()" class="test-btn success">
          <i class="fas fa-check-circle"></i>
          Succès
        </button>
        <button (click)="testErrorNotification()" class="test-btn error">
          <i class="fas fa-exclamation-circle"></i>
          Erreur
        </button>
        <button (click)="testWarningNotification()" class="test-btn warning">
          <i class="fas fa-exclamation-triangle"></i>
          Attention
        </button>
        <button (click)="testInfoNotification()" class="test-btn info">
          <i class="fas fa-info-circle"></i>
          Information
        </button>
      </div>
    </div>
    
    <!-- Tests des templates -->
    <div class="test-group">
      <h3 class="group-title">Templates de notifications</h3>
      <div class="test-buttons">
        <button (click)="testMessageTemplate()" class="test-btn template">
          <i class="fas fa-envelope"></i>
          Message reçu
        </button>
        <button (click)="testCallTemplate()" class="test-btn template">
          <i class="fas fa-phone"></i>
          Appel entrant
        </button>
        <button (click)="testSystemTemplate()" class="test-btn template">
          <i class="fas fa-download"></i>
          Mise à jour
        </button>
      </div>
    </div>
    
    <!-- Tests avancés -->
    <div class="test-group">
      <h3 class="group-title">Fonctionnalités avancées</h3>
      <div class="test-buttons">
        <button (click)="testProgressNotification()" class="test-btn advanced">
          <i class="fas fa-progress-bar"></i>
          Progression
        </button>
        <button (click)="testCustomNotification()" class="test-btn advanced">
          <i class="fas fa-cogs"></i>
          Avec actions
        </button>
        <button (click)="clearAllNotifications()" class="test-btn clear">
          <i class="fas fa-trash"></i>
          Tout effacer
        </button>
      </div>
    </div>
    
    <!-- Paramètres -->
    <div class="test-group">
      <h3 class="group-title">Paramètres</h3>
      <div class="test-buttons">
        <button (click)="toggleNotifications()" class="test-btn setting">
          <i class="fas fa-toggle-on"></i>
          Activer/Désactiver
        </button>
        <button (click)="toggleSound()" class="test-btn setting">
          <i class="fas fa-volume-up"></i>
          Son On/Off
        </button>
        <button (click)="toggleBrowserNotifications()" class="test-btn setting">
          <i class="fas fa-browser"></i>
          Navigateur On/Off
        </button>
      </div>
    </div>
  </div>
  
  <!-- Section du chat -->
  <div class="test-section">
    <h2 class="section-title">
      <i class="fas fa-comments"></i>
      Interface de Chat
    </h2>
    
    <!-- Composant de chat -->
    <div class="chat-container">
      <app-message-chat 
        [currentUserId]="currentUserId"
        [selectedConversationId]="selectedConversationId">
      </app-message-chat>
    </div>
  </div>
  
  <!-- Instructions -->
  <div class="test-section">
    <h2 class="section-title">
      <i class="fas fa-info"></i>
      Instructions
    </h2>
    
    <div class="instructions">
      <div class="instruction-item">
        <i class="fas fa-1"></i>
        <div>
          <strong>Notifications :</strong> Testez les différents types de notifications en cliquant sur les boutons ci-dessus.
        </div>
      </div>
      
      <div class="instruction-item">
        <i class="fas fa-2"></i>
        <div>
          <strong>Templates :</strong> Les templates utilisent des variables pour personnaliser le contenu.
        </div>
      </div>
      
      <div class="instruction-item">
        <i class="fas fa-3"></i>
        <div>
          <strong>Chat :</strong> L'interface de chat inclut la liste des conversations, l'envoi de messages, et les fonctionnalités avancées.
        </div>
      </div>
      
      <div class="instruction-item">
        <i class="fas fa-4"></i>
        <div>
          <strong>Temps réel :</strong> Les notifications apparaissent en temps réel et peuvent inclure des actions interactives.
        </div>
      </div>
      
      <div class="instruction-item">
        <i class="fas fa-5"></i>
        <div>
          <strong>Paramètres :</strong> Vous pouvez activer/désactiver les notifications, le son, et les notifications navigateur.
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Composant d'affichage des notifications -->
<app-notification-display></app-notification-display>
