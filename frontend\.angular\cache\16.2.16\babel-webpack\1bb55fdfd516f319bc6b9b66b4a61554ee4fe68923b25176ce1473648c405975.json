{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { CommonModule } from '@angular/common';\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { LayoutsModule } from './layouts/layouts.module';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { HttpClientModule } from '@angular/common/http';\nimport { JwtModule, JWT_OPTIONS } from '@auth0/angular-jwt';\nimport { environment } from 'src/environments/environment';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\n// import { AiChatbotModule } from './components/ai-chatbot/ai-chatbot.module';\nimport { GraphQLModule } from './graphql.module';\nimport { ApolloModule } from 'apollo-angular';\n// Modules temporairement commentés - à créer si nécessaire\n// import { CallModule } from './components/call/call.module';\n// import { ConnectionStatusModule } from './components/connection-status/connection-status.module';\n// import { GraphqlStatusModule } from './components/graphql-status/graphql-status.module';\n// import { VoiceMessageModule } from './components/voice-message/voice-message.module';\nimport { SharedModule } from './shared/shared.module';\nimport { CalendarModule, DateAdapter } from 'angular-calendar';\nimport { adapterFactory } from 'angular-calendar/date-adapters/date-fns';\n// Factory simplifiée sans injection de JwtHelperService\nexport function jwtOptionsFactory() {\n  return {\n    tokenGetter: () => {\n      // Supprimer les logs pour améliorer les performances\n      // if (!environment.production) {\n      //   console.debug('JWT token retrieved from storage');\n      // }\n      return localStorage.getItem('token');\n    },\n    allowedDomains: [new URL(environment.urlBackend).hostname],\n    disallowedRoutes: [`${new URL(environment.urlBackend).origin}/users/login`]\n  };\n}\nexport let AppModule = class AppModule {};\nAppModule = __decorate([NgModule({\n  declarations: [AppComponent],\n  imports: [BrowserModule, CommonModule, AppRoutingModule, LayoutsModule, FormsModule, ReactiveFormsModule, HttpClientModule, BrowserAnimationsModule,\n  // AiChatbotModule,\n  JwtModule.forRoot({\n    jwtOptionsProvider: {\n      provide: JWT_OPTIONS,\n      useFactory: jwtOptionsFactory\n    }\n  }), GraphQLModule, ApolloModule,\n  // CallModule,\n  // ConnectionStatusModule,\n  // GraphqlStatusModule,\n  // VoiceMessageModule,\n  SharedModule, CalendarModule.forRoot({\n    provide: DateAdapter,\n    useFactory: adapterFactory\n  })],\n  providers: [],\n  bootstrap: [AppComponent]\n})], AppModule);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}