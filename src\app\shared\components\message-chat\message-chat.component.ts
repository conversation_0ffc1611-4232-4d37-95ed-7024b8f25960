import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>ementRef,
  Input,
} from "@angular/core";
import { Subscription, BehaviorSubject } from "rxjs";
import { AdvancedNotificationService } from "../../../services/advanced-notification.service";

export interface Message {
  id: string;
  content: string;
  senderId: string;
  senderName: string;
  receiverId: string;
  conversationId: string;
  timestamp: Date;
  type: "text" | "image" | "voice" | "file" | "location";
  attachments?: MessageAttachment[];
  reactions?: MessageReaction[];
  isRead: boolean;
  isEdited: boolean;
  editedAt?: Date;
  replyTo?: string;
  isForwarded: boolean;
  forwardedFrom?: string;
  isEphemeral: boolean;
  expiresAt?: Date;
}

export interface MessageAttachment {
  id: string;
  name: string;
  type: string;
  size: number;
  url: string;
  thumbnailUrl?: string;
}

export interface MessageReaction {
  id: string;
  emoji: string;
  userId: string;
  userName: string;
  timestamp: Date;
}

export interface Conversation {
  id: string;
  name: string;
  participants: User[];
  lastMessage?: Message;
  unreadCount: number;
  isGroup: boolean;
  avatar?: string;
  isOnline: boolean;
  lastSeen?: Date;
  isTyping: boolean;
}

export interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  isOnline: boolean;
  lastSeen?: Date;
}

@Component({
  selector: "app-message-chat",
  templateUrl: "./message-chat.component.html",
  styleUrls: ["./message-chat.component.css"],
})
export class MessageChatComponent implements OnInit, OnDestroy {
  @ViewChild("messagesContainer") messagesContainer!: ElementRef;
  @ViewChild("messageInput") messageInput!: ElementRef;
  @ViewChild("fileInput") fileInput!: ElementRef;

  @Input() currentUserId: string = "";
  @Input() selectedConversationId: string = "";

  // État du composant
  messages: Message[] = [];
  conversations: Conversation[] = [];
  selectedConversation?: Conversation;
  currentMessage = "";
  isTyping = false;
  isLoading = false;
  isRecording = false;

  // Fonctionnalités avancées
  replyToMessage?: Message;
  editingMessage?: Message;
  selectedMessages: string[] = [];
  showEmojiPicker = false;
  showAttachmentMenu = false;
  showMessageScheduler = false;

  // Recherche et filtres
  searchQuery = "";
  messageFilter: "all" | "unread" | "media" | "files" = "all";

  // Pagination
  currentPage = 1;
  messagesPerPage = 50;
  hasMoreMessages = true;

  // Subscriptions
  private subscriptions: Subscription[] = [];
  private typingTimeout?: any;

  // Observables
  private typingUsers$ = new BehaviorSubject<string[]>([]);

  constructor(private notificationService: AdvancedNotificationService) {}

  ngOnInit(): void {
    this.initializeChat();
    this.loadConversations();
    this.setupRealTimeListeners();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((sub) => sub.unsubscribe());
    if (this.typingTimeout) {
      clearTimeout(this.typingTimeout);
    }
  }

  private initializeChat(): void {
    // Initialiser les données de test
    this.loadMockData();

    if (this.selectedConversationId) {
      this.selectConversation(this.selectedConversationId);
    }
  }

  private loadConversations(): void {
    // Simuler le chargement des conversations
    // À remplacer par un appel API réel
    console.log("Loading conversations...");
  }

  private setupRealTimeListeners(): void {
    // Configurer les listeners WebSocket/Socket.IO
    // À implémenter avec le service de messagerie réel
    console.log("Setting up real-time listeners...");
  }

  private loadMockData(): void {
    // Données de test
    this.conversations = [
      {
        id: "1",
        name: "John Doe",
        participants: [
          {
            id: "1",
            name: "John Doe",
            email: "<EMAIL>",
            isOnline: true,
          },
          {
            id: this.currentUserId,
            name: "Moi",
            email: "<EMAIL>",
            isOnline: true,
          },
        ],
        unreadCount: 2,
        isGroup: false,
        isOnline: true,
        isTyping: false,
      },
      {
        id: "2",
        name: "Équipe Projet",
        participants: [
          {
            id: "2",
            name: "Alice Smith",
            email: "<EMAIL>",
            isOnline: true,
          },
          {
            id: "3",
            name: "Bob Johnson",
            email: "<EMAIL>",
            isOnline: false,
            lastSeen: new Date(Date.now() - 3600000),
          },
          {
            id: this.currentUserId,
            name: "Moi",
            email: "<EMAIL>",
            isOnline: true,
          },
        ],
        unreadCount: 0,
        isGroup: true,
        isOnline: true,
        isTyping: false,
      },
    ];

    this.messages = [
      {
        id: "1",
        content: "Salut! Comment ça va?",
        senderId: "1",
        senderName: "John Doe",
        receiverId: this.currentUserId,
        conversationId: "1",
        timestamp: new Date(Date.now() - 3600000),
        type: "text",
        isRead: false,
        isEdited: false,
        isForwarded: false,
        isEphemeral: false,
        reactions: [],
      },
      {
        id: "2",
        content: "Ça va bien, merci! Et toi?",
        senderId: this.currentUserId,
        senderName: "Moi",
        receiverId: "1",
        conversationId: "1",
        timestamp: new Date(Date.now() - 3500000),
        type: "text",
        isRead: true,
        isEdited: false,
        isForwarded: false,
        isEphemeral: false,
        reactions: [],
      },
    ];
  }

  selectConversation(conversationId: string): void {
    this.selectedConversation = this.conversations.find(
      (c) => c.id === conversationId
    );
    this.selectedConversationId = conversationId;
    this.loadMessages(conversationId);
    this.markConversationAsRead(conversationId);
  }

  private loadMessages(conversationId: string): void {
    this.isLoading = true;

    // Simuler le chargement des messages
    setTimeout(() => {
      this.messages = this.messages.filter(
        (m) => m.conversationId === conversationId
      );
      this.isLoading = false;
      this.scrollToBottom();
    }, 500);
  }

  private markConversationAsRead(conversationId: string): void {
    const conversation = this.conversations.find(
      (c) => c.id === conversationId
    );
    if (conversation) {
      conversation.unreadCount = 0;
    }

    // Marquer les messages comme lus
    this.messages
      .filter(
        (m) =>
          m.conversationId === conversationId &&
          !m.isRead &&
          m.senderId !== this.currentUserId
      )
      .forEach((m) => (m.isRead = true));
  }

  sendMessage(): void {
    if (!this.currentMessage.trim() || !this.selectedConversation) {
      return;
    }

    const newMessage: Message = {
      id: Date.now().toString(),
      content: this.currentMessage.trim(),
      senderId: this.currentUserId,
      senderName: "Moi",
      receiverId:
        this.selectedConversation.participants.find(
          (p) => p.id !== this.currentUserId
        )?.id || "",
      conversationId: this.selectedConversation.id,
      timestamp: new Date(),
      type: "text",
      isRead: false,
      isEdited: false,
      isForwarded: false,
      isEphemeral: false,
      reactions: [],
      replyTo: this.replyToMessage?.id,
    };

    this.messages.push(newMessage);
    this.currentMessage = "";
    this.replyToMessage = undefined;

    this.scrollToBottom();
    this.stopTyping();

    // Simuler l'envoi via API
    this.sendMessageToServer(newMessage);
  }

  private sendMessageToServer(message: Message): void {
    // Simuler l'envoi au serveur
    console.log("Sending message to server:", message);

    // Simuler une notification pour le destinataire
    setTimeout(() => {
      this.notificationService.showFromTemplate("message_received", {
        sender: message.senderName,
        message: message.content,
      });
    }, 1000);
  }

  onMessageInput(): void {
    this.startTyping();
  }

  private startTyping(): void {
    if (!this.isTyping) {
      this.isTyping = true;
      // Envoyer l'indicateur de frappe via WebSocket
      console.log("User started typing...");
    }

    // Réinitialiser le timeout
    if (this.typingTimeout) {
      clearTimeout(this.typingTimeout);
    }

    this.typingTimeout = setTimeout(() => {
      this.stopTyping();
    }, 3000);
  }

  private stopTyping(): void {
    if (this.isTyping) {
      this.isTyping = false;
      // Arrêter l'indicateur de frappe via WebSocket
      console.log("User stopped typing...");
    }

    if (this.typingTimeout) {
      clearTimeout(this.typingTimeout);
      this.typingTimeout = undefined;
    }
  }

  private scrollToBottom(): void {
    setTimeout(() => {
      if (this.messagesContainer) {
        const element = this.messagesContainer.nativeElement;
        element.scrollTop = element.scrollHeight;
      }
    }, 100);
  }

  // Méthodes pour les fonctionnalités avancées
  replyToMessageAction(message: Message): void {
    this.replyToMessage = message;
    this.messageInput?.nativeElement.focus();
  }

  editMessage(message: Message): void {
    if (message.senderId === this.currentUserId) {
      this.editingMessage = message;
      this.currentMessage = message.content;
      this.messageInput?.nativeElement.focus();
    }
  }

  deleteMessage(message: Message): void {
    if (message.senderId === this.currentUserId) {
      const index = this.messages.findIndex((m) => m.id === message.id);
      if (index > -1) {
        this.messages.splice(index, 1);
      }
    }
  }

  forwardMessage(message: Message): void {
    // Implémenter la logique de transfert
    console.log("Forwarding message:", message);
  }

  addReaction(message: Message, emoji: string): void {
    if (!message.reactions) {
      message.reactions = [];
    }

    // Vérifier si l'utilisateur a déjà réagi avec cet emoji
    const existingReaction = message.reactions.find(
      (r) => r.userId === this.currentUserId && r.emoji === emoji
    );

    if (existingReaction) {
      // Supprimer la réaction
      message.reactions = message.reactions.filter(
        (r) => r !== existingReaction
      );
    } else {
      // Ajouter la réaction
      message.reactions.push({
        id: Date.now().toString(),
        emoji,
        userId: this.currentUserId,
        userName: "Moi",
        timestamp: new Date(),
      });
    }
  }

  onFileSelected(event: any): void {
    const files = event.target.files;
    if (files && files.length > 0) {
      for (let file of files) {
        this.uploadFile(file);
      }
    }
  }

  private uploadFile(file: File): void {
    // Simuler l'upload de fichier
    console.log("Uploading file:", file.name);

    const attachment: MessageAttachment = {
      id: Date.now().toString(),
      name: file.name,
      type: file.type,
      size: file.size,
      url: URL.createObjectURL(file),
    };

    const message: Message = {
      id: Date.now().toString(),
      content: `Fichier partagé: ${file.name}`,
      senderId: this.currentUserId,
      senderName: "Moi",
      receiverId:
        this.selectedConversation?.participants.find(
          (p) => p.id !== this.currentUserId
        )?.id || "",
      conversationId: this.selectedConversationId,
      timestamp: new Date(),
      type: file.type.startsWith("image/") ? "image" : "file",
      attachments: [attachment],
      isRead: false,
      isEdited: false,
      isForwarded: false,
      isEphemeral: false,
      reactions: [],
    };

    this.messages.push(message);
    this.scrollToBottom();
  }

  isMyMessage(message: Message): boolean {
    return message.senderId === this.currentUserId;
  }

  getMessageTime(timestamp: Date): string {
    return new Date(timestamp).toLocaleTimeString("fr-FR", {
      hour: "2-digit",
      minute: "2-digit",
    });
  }

  getConversationAvatar(conversation: Conversation): string {
    return conversation.avatar || "/assets/images/default-avatar.png";
  }

  getLastMessagePreview(conversation: Conversation): string {
    if (conversation.lastMessage) {
      return conversation.lastMessage.content.substring(0, 50) + "...";
    }
    return "Aucun message";
  }

  trackByMessageId(index: number, message: Message): string {
    return message.id;
  }

  trackByConversationId(index: number, conversation: Conversation): string {
    return conversation.id;
  }

  // Méthodes pour les composants enfants
  onVoiceRecorded(audioBlob: Blob): void {
    // Traiter l'enregistrement vocal
    const voiceMessage: Message = {
      id: Date.now().toString(),
      content: "Message vocal",
      senderId: this.currentUserId,
      senderName: "Moi",
      receiverId:
        this.selectedConversation?.participants.find(
          (p) => p.id !== this.currentUserId
        )?.id || "",
      conversationId: this.selectedConversationId,
      timestamp: new Date(),
      type: "voice",
      attachments: [
        {
          id: Date.now().toString(),
          name: "voice-message.webm",
          type: "audio/webm",
          size: audioBlob.size,
          url: URL.createObjectURL(audioBlob),
        },
      ],
      isRead: false,
      isEdited: false,
      isForwarded: false,
      isEphemeral: false,
      reactions: [],
    };

    this.messages.push(voiceMessage);
    this.scrollToBottom();
  }

  onLocationShared(location: {
    lat: number;
    lng: number;
    address: string;
  }): void {
    const locationMessage: Message = {
      id: Date.now().toString(),
      content: `Localisation partagée: ${location.address}`,
      senderId: this.currentUserId,
      senderName: "Moi",
      receiverId:
        this.selectedConversation?.participants.find(
          (p) => p.id !== this.currentUserId
        )?.id || "",
      conversationId: this.selectedConversationId,
      timestamp: new Date(),
      type: "location",
      attachments: [
        {
          id: Date.now().toString(),
          name: "location.json",
          type: "application/json",
          size: 0,
          url: `data:application/json,${encodeURIComponent(
            JSON.stringify(location)
          )}`,
        },
      ],
      isRead: false,
      isEdited: false,
      isForwarded: false,
      isEphemeral: false,
      reactions: [],
    };

    this.messages.push(locationMessage);
    this.scrollToBottom();
  }

  // Gestion des erreurs
  onError(error: any): void {
    console.error("Chat error:", error);
    this.notificationService.error(
      "Erreur",
      "Une erreur est survenue dans le chat."
    );
  }
}
