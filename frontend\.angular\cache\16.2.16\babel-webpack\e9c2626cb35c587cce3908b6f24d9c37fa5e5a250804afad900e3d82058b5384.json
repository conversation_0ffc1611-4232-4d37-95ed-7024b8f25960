{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../services/message.service\";\nimport * as i2 from \"../../../../services/auth.service\";\nimport * as i3 from \"../../../../services/toast.service\";\nimport * as i4 from \"../../../../services/theme.service\";\nimport * as i5 from \"../../../../services/mock-data.service\";\nimport * as i6 from \"@angular/router\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"@angular/forms\";\nconst _c0 = [\"searchInput\"];\nfunction MessageLayoutComponent_div_15_div_3_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 46);\n  }\n}\nfunction MessageLayoutComponent_div_15_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵlistener(\"click\", function MessageLayoutComponent_div_15_div_3_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r13);\n      const theme_r10 = restoredCtx.$implicit;\n      const ctx_r12 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r12.selectTheme(theme_r10.name));\n    });\n    i0.ɵɵelement(1, \"div\", 43);\n    i0.ɵɵelementStart(2, \"span\", 44);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, MessageLayoutComponent_div_15_div_3_i_4_Template, 1, 0, \"i\", 45);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const theme_r10 = ctx.$implicit;\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleProp(\"background\", theme_r10.gradients.primary);\n    i0.ɵɵclassProp(\"border-white\", (ctx_r9.currentTheme == null ? null : ctx_r9.currentTheme.name) === theme_r10.name)(\"border-gray-500\", (ctx_r9.currentTheme == null ? null : ctx_r9.currentTheme.name) !== theme_r10.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(theme_r10.displayName);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r9.currentTheme == null ? null : ctx_r9.currentTheme.name) === theme_r10.name);\n  }\n}\nfunction MessageLayoutComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"div\", 40);\n    i0.ɵɵtext(2, \" Choisir un th\\u00E8me \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, MessageLayoutComponent_div_15_div_3_Template, 5, 8, \"div\", 41);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.availableThemes);\n  }\n}\nfunction MessageLayoutComponent_button_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function MessageLayoutComponent_button_23_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.toggleSearchFilters());\n    });\n    i0.ɵɵelement(1, \"i\", 48);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageLayoutComponent_button_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 49);\n    i0.ɵɵlistener(\"click\", function MessageLayoutComponent_button_24_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.clearSearch());\n    });\n    i0.ɵɵelement(1, \"i\", 50);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageLayoutComponent_div_25_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 55);\n    i0.ɵɵlistener(\"click\", function MessageLayoutComponent_div_25_button_2_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r22);\n      const filter_r20 = restoredCtx.$implicit;\n      const ctx_r21 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r21.toggleSearchFilter(filter_r20.key));\n    });\n    i0.ɵɵelement(1, \"i\", 56);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r20 = ctx.$implicit;\n    i0.ɵɵclassProp(\"bg-blue-600\", filter_r20.active)(\"bg-gray-600\", !filter_r20.active)(\"text-white\", filter_r20.active)(\"text-gray-300\", !filter_r20.active);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(filter_r20.icon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", filter_r20.label, \" \");\n  }\n}\nfunction MessageLayoutComponent_div_25_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r19 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r19.searchResults.length, \" r\\u00E9sultat(s) trouv\\u00E9(s) \");\n  }\n}\nfunction MessageLayoutComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51)(1, \"div\", 52);\n    i0.ɵɵtemplate(2, MessageLayoutComponent_div_25_button_2_Template, 3, 11, \"button\", 53);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, MessageLayoutComponent_div_25_div_3_Template, 2, 1, \"div\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.searchFilters);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.searchResults.length > 0);\n  }\n}\nfunction MessageLayoutComponent_span_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 58);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r5.notifications.length > 9 ? \"9+\" : ctx_r5.notifications.length, \" \");\n  }\n}\nfunction MessageLayoutComponent_div_41_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 66);\n    i0.ɵɵlistener(\"click\", function MessageLayoutComponent_div_41_div_1_div_3_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r29);\n      const result_r27 = restoredCtx.$implicit;\n      const ctx_r28 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r28.selectConversation(result_r27));\n    });\n    i0.ɵɵelementStart(1, \"div\", 4);\n    i0.ɵɵelement(2, \"img\", 67);\n    i0.ɵɵelementStart(3, \"div\", 68)(4, \"h4\", 69);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 70);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const result_r27 = ctx.$implicit;\n    const ctx_r26 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r26.getConversationAvatar(result_r27), i0.ɵɵsanitizeUrl)(\"alt\", ctx_r26.getConversationName(result_r27));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r26.getConversationName(result_r27), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r26.getLastMessagePreview(result_r27), \" \");\n  }\n}\nfunction MessageLayoutComponent_div_41_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63)(1, \"div\", 64);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, MessageLayoutComponent_div_41_div_1_div_3_Template, 8, 4, \"div\", 65);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r23 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" R\\u00E9sultats de recherche (\", ctx_r23.searchResults.length, \") \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r23.searchResults);\n  }\n}\nfunction MessageLayoutComponent_div_41_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71);\n    i0.ɵɵelement(1, \"i\", 72);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Aucun r\\u00E9sultat trouv\\u00E9\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessageLayoutComponent_div_41_div_3_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 76);\n    i0.ɵɵelement(1, \"div\", 77);\n    i0.ɵɵelementStart(2, \"p\", 78);\n    i0.ɵɵtext(3, \"Chargement des conversations...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessageLayoutComponent_div_41_div_3_div_2_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 85);\n  }\n}\nfunction MessageLayoutComponent_div_41_div_3_div_2_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 86);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const conversation_r34 = i0.ɵɵnextContext().$implicit;\n    const ctx_r36 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r36.getUnreadCount(conversation_r34) > 99 ? \"99+\" : ctx_r36.getUnreadCount(conversation_r34), \" \");\n  }\n}\nfunction MessageLayoutComponent_div_41_div_3_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r39 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 79);\n    i0.ɵɵlistener(\"click\", function MessageLayoutComponent_div_41_div_3_div_2_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r39);\n      const conversation_r34 = restoredCtx.$implicit;\n      const ctx_r38 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r38.selectConversation(conversation_r34));\n    });\n    i0.ɵɵelementStart(1, \"div\", 4)(2, \"div\", 9);\n    i0.ɵɵelement(3, \"img\", 67);\n    i0.ɵɵtemplate(4, MessageLayoutComponent_div_41_div_3_div_2_div_4_Template, 1, 0, \"div\", 80);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 68)(6, \"div\", 81)(7, \"h4\", 69);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 82);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 83)(12, \"p\", 70);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, MessageLayoutComponent_div_41_div_3_div_2_span_14_Template, 2, 1, \"span\", 84);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const conversation_r34 = ctx.$implicit;\n    const ctx_r31 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"bg-gray-700\", ctx_r31.selectedConversationId === conversation_r34.id)(\"border-l-4\", ctx_r31.selectedConversationId === conversation_r34.id)(\"border-blue-500\", ctx_r31.selectedConversationId === conversation_r34.id);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", ctx_r31.getConversationAvatar(conversation_r34), i0.ɵɵsanitizeUrl)(\"alt\", ctx_r31.getConversationName(conversation_r34));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !conversation_r34.isGroup && ctx_r31.isUserOnline(conversation_r34.participants == null ? null : conversation_r34.participants[0]));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r31.getConversationName(conversation_r34), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r31.formatLastMessageTime(conversation_r34.lastMessage == null ? null : conversation_r34.lastMessage.timestamp), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r31.getLastMessagePreview(conversation_r34), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r31.getUnreadCount(conversation_r34) > 0);\n  }\n}\nfunction MessageLayoutComponent_div_41_div_3_div_3_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Charger plus\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageLayoutComponent_div_41_div_3_div_3_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Chargement...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageLayoutComponent_div_41_div_3_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r43 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 87)(1, \"button\", 88);\n    i0.ɵɵlistener(\"click\", function MessageLayoutComponent_div_41_div_3_div_3_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r43);\n      const ctx_r42 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r42.loadMoreConversations());\n    });\n    i0.ɵɵtemplate(2, MessageLayoutComponent_div_41_div_3_div_3_span_2_Template, 2, 0, \"span\", 62);\n    i0.ɵɵtemplate(3, MessageLayoutComponent_div_41_div_3_div_3_span_3_Template, 2, 0, \"span\", 62);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r32 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r32.isLoadingConversations);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r32.isLoadingConversations);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r32.isLoadingConversations);\n  }\n}\nfunction MessageLayoutComponent_div_41_div_3_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71);\n    i0.ɵɵelement(1, \"i\", 89);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Aucune conversation\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 90);\n    i0.ɵɵtext(5, \" Commencez une nouvelle conversation dans l'onglet Contacts \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessageLayoutComponent_div_41_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, MessageLayoutComponent_div_41_div_3_div_1_Template, 4, 0, \"div\", 73);\n    i0.ɵɵtemplate(2, MessageLayoutComponent_div_41_div_3_div_2_Template, 15, 13, \"div\", 74);\n    i0.ɵɵtemplate(3, MessageLayoutComponent_div_41_div_3_div_3_Template, 4, 3, \"div\", 75);\n    i0.ɵɵtemplate(4, MessageLayoutComponent_div_41_div_3_div_4_Template, 6, 0, \"div\", 61);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r25 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r25.isLoadingConversations && ctx_r25.conversations.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r25.conversations)(\"ngForTrackBy\", ctx_r25.trackByConversationId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r25.hasMoreConversations);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r25.conversations.length === 0 && !ctx_r25.isLoadingConversations);\n  }\n}\nfunction MessageLayoutComponent_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵtemplate(1, MessageLayoutComponent_div_41_div_1_Template, 4, 2, \"div\", 60);\n    i0.ɵɵtemplate(2, MessageLayoutComponent_div_41_div_2_Template, 4, 0, \"div\", 61);\n    i0.ɵɵtemplate(3, MessageLayoutComponent_div_41_div_3_Template, 5, 5, \"div\", 62);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.isSearching && ctx_r6.searchResults.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.isSearching && ctx_r6.searchResults.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r6.isSearching);\n  }\n}\nfunction MessageLayoutComponent_div_42_div_1_div_3_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 85);\n  }\n}\nfunction MessageLayoutComponent_div_42_div_1_div_3_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"div\", 9);\n    i0.ɵɵelement(2, \"img\", 67);\n    i0.ɵɵtemplate(3, MessageLayoutComponent_div_42_div_1_div_3_div_1_div_3_Template, 1, 0, \"div\", 80);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 68)(5, \"h4\", 69);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 70);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 95);\n    i0.ɵɵelement(10, \"i\", 96);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const result_r48 = i0.ɵɵnextContext().$implicit;\n    const ctx_r49 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", result_r48.image || \"/assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", result_r48.username);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r49.isUserOnline(result_r48));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", result_r48.username, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(result_r48.email);\n  }\n}\nfunction MessageLayoutComponent_div_42_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r53 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 93);\n    i0.ɵɵlistener(\"click\", function MessageLayoutComponent_div_42_div_1_div_3_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r53);\n      const result_r48 = restoredCtx.$implicit;\n      const ctx_r52 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r52.isUser(result_r48) ? ctx_r52.startConversationWithUser(result_r48) : null);\n    });\n    i0.ɵɵtemplate(1, MessageLayoutComponent_div_42_div_1_div_3_div_1_Template, 11, 5, \"div\", 94);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const result_r48 = ctx.$implicit;\n    const ctx_r47 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r47.isUser(result_r48));\n  }\n}\nfunction MessageLayoutComponent_div_42_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63)(1, \"div\", 64);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, MessageLayoutComponent_div_42_div_1_div_3_Template, 2, 1, \"div\", 92);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r44 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" R\\u00E9sultats de recherche (\", ctx_r44.searchResults.length, \") \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r44.searchResults);\n  }\n}\nfunction MessageLayoutComponent_div_42_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71);\n    i0.ɵɵelement(1, \"i\", 72);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Aucun utilisateur trouv\\u00E9\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessageLayoutComponent_div_42_div_3_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 76);\n    i0.ɵɵelement(1, \"div\", 77);\n    i0.ɵɵelementStart(2, \"p\", 78);\n    i0.ɵɵtext(3, \"Chargement des utilisateurs...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessageLayoutComponent_div_42_div_3_div_2_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 85);\n  }\n}\nfunction MessageLayoutComponent_div_42_div_3_div_2_p_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 102);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const user_r58 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", user_r58.role, \" \");\n  }\n}\nfunction MessageLayoutComponent_div_42_div_3_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r63 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 93);\n    i0.ɵɵlistener(\"click\", function MessageLayoutComponent_div_42_div_3_div_2_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r63);\n      const user_r58 = restoredCtx.$implicit;\n      const ctx_r62 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r62.startConversationWithUser(user_r58));\n    });\n    i0.ɵɵelementStart(1, \"div\", 4)(2, \"div\", 9);\n    i0.ɵɵelement(3, \"img\", 67);\n    i0.ɵɵtemplate(4, MessageLayoutComponent_div_42_div_3_div_2_div_4_Template, 1, 0, \"div\", 80);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 68)(6, \"h4\", 69);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 70);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, MessageLayoutComponent_div_42_div_3_div_2_p_10_Template, 2, 1, \"p\", 98);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 99)(12, \"div\", 100);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 101);\n    i0.ɵɵelement(15, \"i\", 96);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const user_r58 = ctx.$implicit;\n    const ctx_r55 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", user_r58.image || \"/assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", user_r58.username);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r55.isUserOnline(user_r58));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", user_r58.username, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(user_r58.email);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", user_r58.role);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"bg-green-600\", ctx_r55.isUserOnline(user_r58))(\"text-green-100\", ctx_r55.isUserOnline(user_r58))(\"bg-gray-600\", !ctx_r55.isUserOnline(user_r58))(\"text-gray-300\", !ctx_r55.isUserOnline(user_r58));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r55.isUserOnline(user_r58) ? \"En ligne\" : \"Hors ligne\", \" \");\n  }\n}\nfunction MessageLayoutComponent_div_42_div_3_div_3_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Charger plus\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageLayoutComponent_div_42_div_3_div_3_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Chargement...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageLayoutComponent_div_42_div_3_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r67 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 87)(1, \"button\", 88);\n    i0.ɵɵlistener(\"click\", function MessageLayoutComponent_div_42_div_3_div_3_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r67);\n      const ctx_r66 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r66.loadMoreUsers());\n    });\n    i0.ɵɵtemplate(2, MessageLayoutComponent_div_42_div_3_div_3_span_2_Template, 2, 0, \"span\", 62);\n    i0.ɵɵtemplate(3, MessageLayoutComponent_div_42_div_3_div_3_span_3_Template, 2, 0, \"span\", 62);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r56 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r56.isLoadingUsers);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r56.isLoadingUsers);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r56.isLoadingUsers);\n  }\n}\nfunction MessageLayoutComponent_div_42_div_3_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71);\n    i0.ɵɵelement(1, \"i\", 103);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Aucun utilisateur trouv\\u00E9\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessageLayoutComponent_div_42_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, MessageLayoutComponent_div_42_div_3_div_1_Template, 4, 0, \"div\", 73);\n    i0.ɵɵtemplate(2, MessageLayoutComponent_div_42_div_3_div_2_Template, 16, 15, \"div\", 97);\n    i0.ɵɵtemplate(3, MessageLayoutComponent_div_42_div_3_div_3_Template, 4, 3, \"div\", 75);\n    i0.ɵɵtemplate(4, MessageLayoutComponent_div_42_div_3_div_4_Template, 4, 0, \"div\", 61);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r46 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r46.isLoadingUsers && ctx_r46.users.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r46.users)(\"ngForTrackBy\", ctx_r46.trackByUserId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r46.hasMoreUsers);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r46.users.length === 0 && !ctx_r46.isLoadingUsers);\n  }\n}\nfunction MessageLayoutComponent_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 91);\n    i0.ɵɵtemplate(1, MessageLayoutComponent_div_42_div_1_Template, 4, 2, \"div\", 60);\n    i0.ɵɵtemplate(2, MessageLayoutComponent_div_42_div_2_Template, 4, 0, \"div\", 61);\n    i0.ɵɵtemplate(3, MessageLayoutComponent_div_42_div_3_Template, 5, 5, \"div\", 62);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.isSearching && ctx_r7.searchResults.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.isSearching && ctx_r7.searchResults.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r7.isSearching);\n  }\n}\nfunction MessageLayoutComponent_div_43_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 76);\n    i0.ɵɵelement(1, \"div\", 77);\n    i0.ɵɵelementStart(2, \"p\", 78);\n    i0.ɵɵtext(3, \"Chargement des notifications...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessageLayoutComponent_div_43_div_2_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 112);\n  }\n}\nfunction MessageLayoutComponent_div_43_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r74 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 106);\n    i0.ɵɵlistener(\"click\", function MessageLayoutComponent_div_43_div_2_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r74);\n      const notification_r71 = restoredCtx.$implicit;\n      const ctx_r73 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r73.markNotificationAsRead(notification_r71));\n    });\n    i0.ɵɵelementStart(1, \"div\", 107)(2, \"div\", 108);\n    i0.ɵɵelement(3, \"i\", 44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 68)(5, \"h4\", 69);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 109);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\", 110);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(11, MessageLayoutComponent_div_43_div_2_div_11_Template, 1, 0, \"div\", 111);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const notification_r71 = ctx.$implicit;\n    const ctx_r69 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"bg-gray-700\", !notification_r71.isRead);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"bg-blue-600\", notification_r71.type === \"NEW_MESSAGE\")(\"bg-green-600\", notification_r71.type === \"FRIEND_REQUEST\")(\"bg-yellow-600\", notification_r71.type === \"GROUP_INVITE\")(\"bg-purple-600\", notification_r71.type === \"MESSAGE_REACTION\")(\"bg-red-600\", notification_r71.type === \"SYSTEM_ALERT\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"fa-message\", notification_r71.type === \"NEW_MESSAGE\")(\"fa-user-plus\", notification_r71.type === \"FRIEND_REQUEST\")(\"fa-users\", notification_r71.type === \"GROUP_INVITE\")(\"fa-heart\", notification_r71.type === \"MESSAGE_REACTION\")(\"fa-exclamation-triangle\", notification_r71.type === \"SYSTEM_ALERT\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r69.getNotificationTitle(notification_r71), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", notification_r71.content, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r69.formatLastMessageTime(notification_r71.timestamp), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !notification_r71.isRead);\n  }\n}\nfunction MessageLayoutComponent_div_43_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71);\n    i0.ɵɵelement(1, \"i\", 113);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Aucune notification\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 90);\n    i0.ɵɵtext(5, \" Vous serez notifi\\u00E9 des nouveaux messages et \\u00E9v\\u00E9nements \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessageLayoutComponent_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 104);\n    i0.ɵɵtemplate(1, MessageLayoutComponent_div_43_div_1_Template, 4, 0, \"div\", 73);\n    i0.ɵɵtemplate(2, MessageLayoutComponent_div_43_div_2_Template, 12, 26, \"div\", 105);\n    i0.ɵɵtemplate(3, MessageLayoutComponent_div_43_div_3_Template, 6, 0, \"div\", 61);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.isLoadingNotifications && ctx_r8.notifications.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r8.notifications)(\"ngForTrackBy\", ctx_r8.trackByNotificationId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.notifications.length === 0 && !ctx_r8.isLoadingNotifications);\n  }\n}\nexport let MessageLayoutComponent = /*#__PURE__*/(() => {\n  class MessageLayoutComponent {\n    constructor(messageService, authService, toastService, themeService, mockDataService, route, router, cdr) {\n      this.messageService = messageService;\n      this.authService = authService;\n      this.toastService = toastService;\n      this.themeService = themeService;\n      this.mockDataService = mockDataService;\n      this.route = route;\n      this.router = router;\n      this.cdr = cdr;\n      // État du composant\n      this.currentUser = null;\n      this.conversations = [];\n      this.users = [];\n      this.notifications = [];\n      // Navigation et UI\n      this.activeTab = 'conversations';\n      this.selectedConversationId = null;\n      this.isMobileMenuOpen = false;\n      this.isSearching = false;\n      // Thème\n      this.currentTheme = null;\n      this.availableThemes = [];\n      this.showThemeSelector = false;\n      // Recherche\n      this.searchQuery = '';\n      this.searchResults = [];\n      this.showSearchFilters = false;\n      this.searchFilters = [{\n        key: 'messages',\n        label: 'Messages',\n        icon: 'fas fa-comment',\n        active: true\n      }, {\n        key: 'users',\n        label: 'Utilisateurs',\n        icon: 'fas fa-user',\n        active: true\n      }, {\n        key: 'groups',\n        label: 'Groupes',\n        icon: 'fas fa-users',\n        active: true\n      }, {\n        key: 'files',\n        label: 'Fichiers',\n        icon: 'fas fa-file',\n        active: false\n      }, {\n        key: 'images',\n        label: 'Images',\n        icon: 'fas fa-image',\n        active: false\n      }, {\n        key: 'recent',\n        label: 'Récents',\n        icon: 'fas fa-clock',\n        active: false\n      }];\n      // États de chargement\n      this.isLoadingConversations = false;\n      this.isLoadingUsers = false;\n      this.isLoadingNotifications = false;\n      // Pagination\n      this.conversationsPage = 1;\n      this.usersPage = 1;\n      this.hasMoreConversations = true;\n      this.hasMoreUsers = true;\n      // Subscriptions\n      this.subscriptions = [];\n      // Observables\n      this.searchQuery$ = new BehaviorSubject('');\n    }\n    ngOnInit() {\n      this.initializeComponent();\n      this.setupSubscriptions();\n      this.loadInitialData();\n    }\n    ngOnDestroy() {\n      this.subscriptions.forEach(sub => sub.unsubscribe());\n    }\n    // ============================================================================\n    // MÉTHODES D'INITIALISATION\n    // ============================================================================\n    initializeComponent() {\n      // Récupérer l'utilisateur actuel\n      this.currentUser = this.authService.getCurrentUser();\n      if (!this.currentUser) {\n        this.router.navigate(['/login']);\n        return;\n      }\n      // Initialiser les thèmes\n      this.currentTheme = this.themeService.getCurrentTheme();\n      this.availableThemes = this.themeService.getAvailableThemes();\n      // Écouter les changements de route\n      this.route.params.subscribe(params => {\n        const conversationId = params['conversationId'];\n        if (conversationId) {\n          this.selectedConversationId = conversationId;\n          this.markConversationAsSelected(conversationId);\n        }\n      });\n    }\n    setupSubscriptions() {\n      // Subscription pour les nouveaux messages\n      const messagesSub = this.messageService.subscribeToMessages().subscribe(message => {\n        if (message) {\n          this.handleNewMessage(message);\n        }\n      });\n      // Subscription pour les notifications\n      const notificationsSub = this.messageService.subscribeToNotifications().subscribe(notification => {\n        if (notification) {\n          this.handleNewNotification(notification);\n        }\n      });\n      // Subscription pour la recherche\n      const searchSub = this.searchQuery$.subscribe(query => {\n        this.performSearch(query);\n      });\n      // Subscription pour les changements de thème\n      const themeSub = this.themeService.currentTheme$.subscribe(theme => {\n        this.currentTheme = theme;\n        this.cdr.detectChanges();\n      });\n      this.subscriptions.push(messagesSub, notificationsSub, searchSub, themeSub);\n    }\n    loadInitialData() {\n      this.loadConversations();\n      this.loadUsers();\n      this.loadNotifications();\n      // Charger l'utilisateur actuel depuis les données de test\n      if (!this.currentUser) {\n        this.currentUser = this.mockDataService.getCurrentUser();\n      }\n    }\n    // ============================================================================\n    // MÉTHODES DE CHARGEMENT DES DONNÉES\n    // ============================================================================\n    loadConversations(page = 1) {\n      if (this.isLoadingConversations) return;\n      this.isLoadingConversations = true;\n      this.messageService.getConversations().subscribe({\n        next: conversations => {\n          if (page === 1) {\n            this.conversations = conversations;\n          } else {\n            this.conversations.push(...conversations);\n          }\n          this.conversationsPage = page;\n          this.hasMoreConversations = conversations.length === 25;\n          this.isLoadingConversations = false;\n          this.cdr.detectChanges();\n        },\n        error: error => {\n          console.warn('Service principal indisponible, utilisation des données de test:', error);\n          // Fallback sur les données de test\n          this.mockDataService.getConversations().subscribe({\n            next: conversations => {\n              if (page === 1) {\n                this.conversations = conversations;\n              } else {\n                this.conversations.push(...conversations);\n              }\n              this.conversationsPage = page;\n              this.hasMoreConversations = false; // Pas de pagination pour les données de test\n              this.isLoadingConversations = false;\n              this.cdr.detectChanges();\n              if (page === 1) {\n                this.toastService.showInfo('Mode démo - Données de test chargées');\n              }\n            },\n            error: mockError => {\n              console.error('Erreur lors du chargement des données de test:', mockError);\n              this.isLoadingConversations = false;\n              this.toastService.showError('Erreur lors du chargement des conversations');\n            }\n          });\n        }\n      });\n    }\n    loadUsers(page = 1) {\n      if (this.isLoadingUsers) return;\n      this.isLoadingUsers = true;\n      this.messageService.getAllUsers(false, '', page, 25).subscribe({\n        next: users => {\n          if (page === 1) {\n            this.users = users;\n          } else {\n            this.users.push(...users);\n          }\n          this.usersPage = page;\n          this.hasMoreUsers = users.length === 25;\n          this.isLoadingUsers = false;\n          this.cdr.detectChanges();\n        },\n        error: error => {\n          console.warn('Service principal indisponible, utilisation des données de test:', error);\n          // Fallback sur les données de test\n          this.mockDataService.getUsers().subscribe({\n            next: users => {\n              if (page === 1) {\n                this.users = users;\n              } else {\n                this.users.push(...users);\n              }\n              this.usersPage = page;\n              this.hasMoreUsers = false; // Pas de pagination pour les données de test\n              this.isLoadingUsers = false;\n              this.cdr.detectChanges();\n            },\n            error: mockError => {\n              console.error('Erreur lors du chargement des données de test:', mockError);\n              this.isLoadingUsers = false;\n              this.toastService.showError('Erreur lors du chargement des utilisateurs');\n            }\n          });\n        }\n      });\n    }\n    loadNotifications() {\n      if (this.isLoadingNotifications) return;\n      this.isLoadingNotifications = true;\n      this.messageService.getNotifications().subscribe({\n        next: notifications => {\n          this.notifications = notifications;\n          this.isLoadingNotifications = false;\n          this.cdr.detectChanges();\n        },\n        error: error => {\n          console.warn('Service principal indisponible, utilisation des données de test:', error);\n          // Fallback sur les données de test\n          this.mockDataService.getNotifications().subscribe({\n            next: notifications => {\n              this.notifications = notifications;\n              this.isLoadingNotifications = false;\n              this.cdr.detectChanges();\n            },\n            error: mockError => {\n              console.error('Erreur lors du chargement des données de test:', mockError);\n              this.isLoadingNotifications = false;\n              this.toastService.showError('Erreur lors du chargement des notifications');\n            }\n          });\n        }\n      });\n    }\n    // ============================================================================\n    // MÉTHODES DE GESTION DES ÉVÉNEMENTS\n    // ============================================================================\n    handleNewMessage(message) {\n      // Mettre à jour la conversation correspondante\n      const conversationIndex = this.conversations.findIndex(conv => conv.id === message.conversationId);\n      if (conversationIndex !== -1) {\n        // Mettre à jour le dernier message\n        this.conversations[conversationIndex].lastMessage = message;\n        // Déplacer la conversation en haut de la liste\n        const conversation = this.conversations.splice(conversationIndex, 1)[0];\n        this.conversations.unshift(conversation);\n        this.cdr.detectChanges();\n      }\n    }\n    handleNewNotification(notification) {\n      // Ajouter la nouvelle notification en haut de la liste\n      this.notifications.unshift(notification);\n      this.cdr.detectChanges();\n      // Afficher une notification toast si ce n'est pas l'onglet actif\n      if (this.activeTab !== 'notifications') {\n        this.toastService.showInfo('Nouvelle notification reçue');\n      }\n    }\n    markConversationAsSelected(conversationId) {\n      // Marquer la conversation comme sélectionnée visuellement\n      this.selectedConversationId = conversationId;\n      this.cdr.detectChanges();\n    }\n    // ============================================================================\n    // MÉTHODES DE NAVIGATION ET UI\n    // ============================================================================\n    switchTab(tab) {\n      this.activeTab = tab;\n      this.searchQuery = '';\n      this.searchResults = [];\n      this.isSearching = false;\n      // Charger les données si nécessaire\n      switch (tab) {\n        case 'conversations':\n          if (this.conversations.length === 0) {\n            this.loadConversations();\n          }\n          break;\n        case 'users':\n          if (this.users.length === 0) {\n            this.loadUsers();\n          }\n          break;\n        case 'notifications':\n          if (this.notifications.length === 0) {\n            this.loadNotifications();\n          }\n          break;\n      }\n    }\n    selectConversation(conversation) {\n      if (!conversation.id) return;\n      this.selectedConversationId = conversation.id;\n      this.router.navigate(['/messages', conversation.id]);\n      // Fermer le menu mobile si ouvert\n      this.isMobileMenuOpen = false;\n    }\n    startConversationWithUser(user) {\n      if (!user.id && !user._id) return;\n      const userId = user.id || user._id;\n      // Créer ou récupérer la conversation avec cet utilisateur\n      this.messageService.createOrGetConversation(userId).subscribe({\n        next: conversation => {\n          this.selectConversation(conversation);\n        },\n        error: error => {\n          console.warn('Service principal indisponible, utilisation des données de test:', error);\n          // Fallback sur les données de test\n          const currentUserId = this.currentUser?.id || '1';\n          this.mockDataService.createConversation(userId, currentUserId).subscribe({\n            next: conversation => {\n              this.conversations.unshift(conversation);\n              this.selectConversation(conversation);\n              this.toastService.showSuccess('Conversation créée (mode démo)');\n            },\n            error: mockError => {\n              console.error('Erreur lors de la création de la conversation:', mockError);\n              this.toastService.showError('Erreur lors de la création de la conversation');\n            }\n          });\n        }\n      });\n    }\n    toggleMobileMenu() {\n      this.isMobileMenuOpen = !this.isMobileMenuOpen;\n    }\n    // ============================================================================\n    // MÉTHODES DE RECHERCHE\n    // ============================================================================\n    onSearchInput(event) {\n      const query = event.target.value.trim();\n      this.searchQuery = query;\n      this.searchQuery$.next(query);\n    }\n    performSearch(query) {\n      if (!query) {\n        this.searchResults = [];\n        this.isSearching = false;\n        return;\n      }\n      this.isSearching = true;\n      if (this.activeTab === 'conversations') {\n        this.searchResults = this.conversations.filter(conv => conv.isGroup ? conv.groupName?.toLowerCase().includes(query.toLowerCase()) : conv.participants?.some(p => p.username?.toLowerCase().includes(query.toLowerCase())));\n      } else if (this.activeTab === 'users') {\n        this.searchResults = this.users.filter(user => user.username?.toLowerCase().includes(query.toLowerCase()) || user.email?.toLowerCase().includes(query.toLowerCase()));\n      }\n      this.cdr.detectChanges();\n    }\n    clearSearch() {\n      this.searchQuery = '';\n      this.searchResults = [];\n      this.isSearching = false;\n      this.showSearchFilters = false;\n      this.searchQuery$.next('');\n    }\n    /**\n     * Affiche/masque les filtres de recherche\n     */\n    toggleSearchFilters() {\n      this.showSearchFilters = !this.showSearchFilters;\n    }\n    /**\n     * Masque les filtres de recherche avec un délai\n     */\n    hideSearchFiltersDelayed() {\n      setTimeout(() => {\n        this.showSearchFilters = false;\n      }, 200);\n    }\n    /**\n     * Active/désactive un filtre de recherche\n     */\n    toggleSearchFilter(filterKey) {\n      const filter = this.searchFilters.find(f => f.key === filterKey);\n      if (filter) {\n        filter.active = !filter.active;\n        // Relancer la recherche avec les nouveaux filtres\n        this.performSearch(this.searchQuery);\n      }\n    }\n    // ============================================================================\n    // MÉTHODES DE PAGINATION\n    // ============================================================================\n    loadMoreConversations() {\n      if (this.hasMoreConversations && !this.isLoadingConversations) {\n        this.loadConversations(this.conversationsPage + 1);\n      }\n    }\n    loadMoreUsers() {\n      if (this.hasMoreUsers && !this.isLoadingUsers) {\n        this.loadUsers(this.usersPage + 1);\n      }\n    }\n    // ============================================================================\n    // MÉTHODES UTILITAIRES POUR LE TEMPLATE\n    // ============================================================================\n    getConversationName(conversation) {\n      if (conversation.isGroup) {\n        return conversation.groupName || 'Groupe sans nom';\n      }\n      if (!this.currentUser) return 'Conversation';\n      const currentUserId = this.currentUser.id || this.currentUser._id;\n      const otherParticipant = conversation.participants?.find(p => (p.id || p._id) !== currentUserId);\n      return otherParticipant?.username || 'Utilisateur inconnu';\n    }\n    getConversationAvatar(conversation) {\n      if (conversation.isGroup) {\n        return conversation.groupPhoto || '/assets/images/default-group.png';\n      }\n      if (!this.currentUser) return '/assets/images/default-avatar.png';\n      const currentUserId = this.currentUser.id || this.currentUser._id;\n      const otherParticipant = conversation.participants?.find(p => (p.id || p._id) !== currentUserId);\n      return otherParticipant?.image || '/assets/images/default-avatar.png';\n    }\n    getLastMessagePreview(conversation) {\n      if (!conversation.lastMessage) return 'Aucun message';\n      const message = conversation.lastMessage;\n      if (message.type === 'TEXT') {\n        return message.content || '';\n      } else if (message.type === 'IMAGE') {\n        return '📷 Image';\n      } else if (message.type === 'FILE') {\n        return '📎 Fichier';\n      } else if (message.type === 'VOICE_MESSAGE') {\n        return '🎤 Message vocal';\n      } else if (message.type === 'VIDEO') {\n        return '🎥 Vidéo';\n      }\n      return 'Message';\n    }\n    formatLastMessageTime(timestamp) {\n      if (!timestamp) return '';\n      const date = new Date(timestamp);\n      const now = new Date();\n      const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);\n      if (diffInHours < 1) {\n        return \"À l'instant\";\n      } else if (diffInHours < 24) {\n        return date.toLocaleTimeString('fr-FR', {\n          hour: '2-digit',\n          minute: '2-digit'\n        });\n      } else if (diffInHours < 168) {\n        // 7 jours\n        return date.toLocaleDateString('fr-FR', {\n          weekday: 'short'\n        });\n      } else {\n        return date.toLocaleDateString('fr-FR', {\n          day: '2-digit',\n          month: '2-digit'\n        });\n      }\n    }\n    getUnreadCount(conversation) {\n      return conversation.unreadCount || 0;\n    }\n    isUserOnline(user) {\n      return user.isOnline || false;\n    }\n    trackByConversationId(index, conversation) {\n      return conversation.id || conversation._id || index.toString();\n    }\n    trackByUserId(index, user) {\n      return user.id || user._id || index.toString();\n    }\n    trackByNotificationId(index, notification) {\n      return notification.id || index.toString();\n    }\n    markNotificationAsRead(notification) {\n      if (!notification.id || notification.isRead) return;\n      this.messageService.markNotificationAsRead(notification.id).subscribe({\n        next: () => {\n          notification.isRead = true;\n          this.cdr.detectChanges();\n        },\n        error: error => {\n          console.error('Erreur lors du marquage de la notification comme lue:', error);\n          this.toastService.showError('Erreur lors du marquage de la notification');\n        }\n      });\n    }\n    // Type guards pour différencier User et Conversation dans les résultats de recherche\n    isUser(item) {\n      return 'username' in item && 'email' in item;\n    }\n    isConversation(item) {\n      return 'participants' in item || 'isGroup' in item;\n    }\n    getNotificationTitle(notification) {\n      switch (notification.type) {\n        case 'NEW_MESSAGE':\n          return 'Nouveau message';\n        case 'FRIEND_REQUEST':\n          return \"Demande d'ami\";\n        case 'GROUP_INVITE':\n          return 'Invitation de groupe';\n        case 'MESSAGE_REACTION':\n          return 'Réaction à un message';\n        case 'SYSTEM_ALERT':\n          return 'Alerte système';\n        default:\n          return 'Notification';\n      }\n    }\n    // ============================================================================\n    // MÉTHODES DE GESTION DES THÈMES\n    // ============================================================================\n    selectTheme(themeName) {\n      this.themeService.setTheme(themeName);\n      this.showThemeSelector = false;\n      this.toastService.showSuccess(`Thème \"${this.themeService.getCurrentTheme().displayName}\" appliqué`);\n    }\n    static {\n      this.ɵfac = function MessageLayoutComponent_Factory(t) {\n        return new (t || MessageLayoutComponent)(i0.ɵɵdirectiveInject(i1.MessageService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.ToastService), i0.ɵɵdirectiveInject(i4.ThemeService), i0.ɵɵdirectiveInject(i5.MockDataService), i0.ɵɵdirectiveInject(i6.ActivatedRoute), i0.ɵɵdirectiveInject(i6.Router), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: MessageLayoutComponent,\n        selectors: [[\"app-message-layout\"]],\n        viewQuery: function MessageLayoutComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.searchInput = _t.first);\n          }\n        },\n        decls: 50,\n        vars: 46,\n        consts: [[1, \"message-layout\", \"h-screen\", \"bg-gray-900\", \"text-white\", \"flex\"], [1, \"sidebar\", \"w-80\", \"bg-gray-800\", \"border-r\", \"border-gray-700\", \"flex\", \"flex-col\"], [1, \"sidebar-header\", \"p-4\", \"border-b\", \"border-gray-700\", \"bg-gray-800\"], [1, \"flex\", \"items-center\", \"justify-between\", \"mb-4\"], [1, \"flex\", \"items-center\", \"space-x-3\"], [1, \"w-10\", \"h-10\", \"rounded-full\", \"border-2\", \"border-blue-500\", 3, \"src\", \"alt\"], [1, \"font-semibold\", \"text-white\"], [1, \"text-sm\", \"text-green-400\"], [1, \"flex\", \"items-center\", \"space-x-2\"], [1, \"relative\"], [\"title\", \"Changer de th\\u00E8me\", 1, \"p-2\", \"rounded-lg\", \"bg-gray-700\", \"hover:bg-gray-600\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-palette\", \"text-blue-400\"], [\"class\", \"absolute top-full right-0 mt-2 bg-gray-800 rounded-lg shadow-lg border border-gray-700 p-2 z-50 min-w-48\", 4, \"ngIf\"], [1, \"md:hidden\", \"p-2\", \"rounded-lg\", \"bg-gray-700\", \"hover:bg-gray-600\", 3, \"click\"], [1, \"fas\", \"fa-times\", \"text-white\"], [\"type\", \"text\", \"placeholder\", \"Rechercher conversations, messages, utilisateurs...\", 1, \"w-full\", \"bg-gray-700\", \"border\", \"border-gray-600\", \"rounded-lg\", \"px-4\", \"py-2\", \"pl-10\", \"pr-20\", \"text-white\", \"placeholder-gray-400\", \"focus:outline-none\", \"focus:border-blue-500\", \"focus:ring-2\", \"focus:ring-blue-500/20\", \"transition-all\", 3, \"ngModel\", \"ngModelChange\", \"input\", \"focus\", \"blur\"], [\"searchInput\", \"\"], [1, \"fas\", \"fa-search\", \"absolute\", \"left-3\", \"top-3\", \"text-gray-400\"], [1, \"absolute\", \"right-2\", \"top-2\", \"flex\", \"items-center\", \"space-x-1\"], [\"class\", \"p-1 text-gray-400 hover:text-blue-400 transition-colors\", \"title\", \"Filtres de recherche\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"p-1 text-gray-400 hover:text-white transition-colors\", \"title\", \"Effacer la recherche\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"mt-2 p-3 bg-gray-700 rounded-lg border border-gray-600 animate-fadeIn\", 4, \"ngIf\"], [1, \"tabs\", \"flex\", \"border-b\", \"border-gray-700\"], [1, \"tab\", \"flex-1\", \"py-3\", \"px-4\", \"text-center\", \"transition-all\", \"duration-200\", 3, \"click\"], [1, \"fas\", \"fa-comments\", \"mb-1\"], [1, \"text-xs\"], [1, \"fas\", \"fa-users\", \"mb-1\"], [1, \"tab\", \"flex-1\", \"py-3\", \"px-4\", \"text-center\", \"transition-all\", \"duration-200\", \"relative\", 3, \"click\"], [1, \"fas\", \"fa-bell\", \"mb-1\"], [\"class\", \"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center\", 4, \"ngIf\"], [1, \"sidebar-content\", \"flex-1\", \"overflow-y-auto\"], [\"class\", \"conversations-list\", 4, \"ngIf\"], [\"class\", \"users-list\", 4, \"ngIf\"], [\"class\", \"notifications-list\", 4, \"ngIf\"], [1, \"main-content\", \"flex-1\", \"flex\", \"flex-col\"], [1, \"md:hidden\", \"p-4\", \"border-b\", \"border-gray-700\", \"bg-gray-800\"], [1, \"p-2\", \"rounded-lg\", \"bg-gray-700\", \"hover:bg-gray-600\", 3, \"click\"], [1, \"fas\", \"fa-bars\", \"text-white\"], [1, \"flex-1\"], [1, \"absolute\", \"top-full\", \"right-0\", \"mt-2\", \"bg-gray-800\", \"rounded-lg\", \"shadow-lg\", \"border\", \"border-gray-700\", \"p-2\", \"z-50\", \"min-w-48\"], [1, \"text-xs\", \"text-gray-400\", \"mb-2\", \"px-2\"], [\"class\", \"flex items-center space-x-3 p-2 hover:bg-gray-700 rounded cursor-pointer transition-colors\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"items-center\", \"space-x-3\", \"p-2\", \"hover:bg-gray-700\", \"rounded\", \"cursor-pointer\", \"transition-colors\", 3, \"click\"], [1, \"w-4\", \"h-4\", \"rounded-full\", \"border-2\"], [1, \"text-white\", \"text-sm\"], [\"class\", \"fas fa-check text-blue-400 text-xs ml-auto\", 4, \"ngIf\"], [1, \"fas\", \"fa-check\", \"text-blue-400\", \"text-xs\", \"ml-auto\"], [\"title\", \"Filtres de recherche\", 1, \"p-1\", \"text-gray-400\", \"hover:text-blue-400\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-filter\", \"text-xs\"], [\"title\", \"Effacer la recherche\", 1, \"p-1\", \"text-gray-400\", \"hover:text-white\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-times\", \"text-xs\"], [1, \"mt-2\", \"p-3\", \"bg-gray-700\", \"rounded-lg\", \"border\", \"border-gray-600\", \"animate-fadeIn\"], [1, \"flex\", \"flex-wrap\", \"gap-2\", \"text-xs\"], [\"class\", \"px-2 py-1 rounded transition-colors\", 3, \"bg-blue-600\", \"bg-gray-600\", \"text-white\", \"text-gray-300\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"mt-2 pt-2 border-t border-gray-600 text-xs text-gray-400\", 4, \"ngIf\"], [1, \"px-2\", \"py-1\", \"rounded\", \"transition-colors\", 3, \"click\"], [1, \"mr-1\"], [1, \"mt-2\", \"pt-2\", \"border-t\", \"border-gray-600\", \"text-xs\", \"text-gray-400\"], [1, \"absolute\", \"-top-1\", \"-right-1\", \"bg-red-500\", \"text-white\", \"text-xs\", \"rounded-full\", \"w-5\", \"h-5\", \"flex\", \"items-center\", \"justify-center\"], [1, \"conversations-list\"], [\"class\", \"search-results\", 4, \"ngIf\"], [\"class\", \"p-8 text-center text-gray-400\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"search-results\"], [1, \"p-3\", \"text-sm\", \"text-gray-400\", \"border-b\", \"border-gray-700\"], [\"class\", \"conversation-item p-4 hover:bg-gray-700 cursor-pointer border-b border-gray-700 transition-colors\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"conversation-item\", \"p-4\", \"hover:bg-gray-700\", \"cursor-pointer\", \"border-b\", \"border-gray-700\", \"transition-colors\", 3, \"click\"], [1, \"w-12\", \"h-12\", \"rounded-full\", 3, \"src\", \"alt\"], [1, \"flex-1\", \"min-w-0\"], [1, \"font-medium\", \"text-white\", \"truncate\"], [1, \"text-sm\", \"text-gray-400\", \"truncate\"], [1, \"p-8\", \"text-center\", \"text-gray-400\"], [1, \"fas\", \"fa-search\", \"text-4xl\", \"mb-4\"], [\"class\", \"p-8 text-center\", 4, \"ngIf\"], [\"class\", \"conversation-item p-4 hover:bg-gray-700 cursor-pointer border-b border-gray-700 transition-colors relative\", 3, \"bg-gray-700\", \"border-l-4\", \"border-blue-500\", \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"p-4 text-center\", 4, \"ngIf\"], [1, \"p-8\", \"text-center\"], [1, \"animate-spin\", \"rounded-full\", \"h-8\", \"w-8\", \"border-b-2\", \"border-blue-500\", \"mx-auto\"], [1, \"text-gray-400\", \"mt-2\"], [1, \"conversation-item\", \"p-4\", \"hover:bg-gray-700\", \"cursor-pointer\", \"border-b\", \"border-gray-700\", \"transition-colors\", \"relative\", 3, \"click\"], [\"class\", \"absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-gray-800\", 4, \"ngIf\"], [1, \"flex\", \"items-center\", \"justify-between\"], [1, \"text-xs\", \"text-gray-400\"], [1, \"flex\", \"items-center\", \"justify-between\", \"mt-1\"], [\"class\", \"bg-blue-500 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center\", 4, \"ngIf\"], [1, \"absolute\", \"bottom-0\", \"right-0\", \"w-3\", \"h-3\", \"bg-green-500\", \"rounded-full\", \"border-2\", \"border-gray-800\"], [1, \"bg-blue-500\", \"text-white\", \"text-xs\", \"rounded-full\", \"px-2\", \"py-1\", \"min-w-[20px]\", \"text-center\"], [1, \"p-4\", \"text-center\"], [1, \"text-blue-400\", \"hover:text-blue-300\", \"disabled:text-gray-500\", 3, \"disabled\", \"click\"], [1, \"fas\", \"fa-comments\", \"text-4xl\", \"mb-4\"], [1, \"text-sm\", \"mt-2\"], [1, \"users-list\"], [\"class\", \"user-item p-4 hover:bg-gray-700 cursor-pointer border-b border-gray-700 transition-colors\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"user-item\", \"p-4\", \"hover:bg-gray-700\", \"cursor-pointer\", \"border-b\", \"border-gray-700\", \"transition-colors\", 3, \"click\"], [\"class\", \"flex items-center space-x-3\", 4, \"ngIf\"], [1, \"text-blue-400\"], [1, \"fas\", \"fa-comment\"], [\"class\", \"user-item p-4 hover:bg-gray-700 cursor-pointer border-b border-gray-700 transition-colors\", 3, \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"text-xs text-gray-500\", 4, \"ngIf\"], [1, \"text-right\"], [1, \"text-xs\", \"px-2\", \"py-1\", \"rounded-full\"], [1, \"text-blue-400\", \"mt-1\"], [1, \"text-xs\", \"text-gray-500\"], [1, \"fas\", \"fa-users\", \"text-4xl\", \"mb-4\"], [1, \"notifications-list\"], [\"class\", \"notification-item p-4 hover:bg-gray-700 cursor-pointer border-b border-gray-700 transition-colors\", 3, \"bg-gray-700\", \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"notification-item\", \"p-4\", \"hover:bg-gray-700\", \"cursor-pointer\", \"border-b\", \"border-gray-700\", \"transition-colors\", 3, \"click\"], [1, \"flex\", \"items-start\", \"space-x-3\"], [1, \"notification-icon\", \"p-2\", \"rounded-full\"], [1, \"text-sm\", \"text-gray-400\", \"mt-1\"], [1, \"text-xs\", \"text-gray-500\", \"mt-2\"], [\"class\", \"w-2 h-2 bg-blue-500 rounded-full\", 4, \"ngIf\"], [1, \"w-2\", \"h-2\", \"bg-blue-500\", \"rounded-full\"], [1, \"fas\", \"fa-bell\", \"text-4xl\", \"mb-4\"]],\n        template: function MessageLayoutComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4);\n            i0.ɵɵelement(5, \"img\", 5);\n            i0.ɵɵelementStart(6, \"div\")(7, \"h3\", 6);\n            i0.ɵɵtext(8);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(9, \"p\", 7);\n            i0.ɵɵtext(10, \"En ligne\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(11, \"div\", 8)(12, \"div\", 9)(13, \"button\", 10);\n            i0.ɵɵlistener(\"click\", function MessageLayoutComponent_Template_button_click_13_listener() {\n              return ctx.showThemeSelector = !ctx.showThemeSelector;\n            });\n            i0.ɵɵelement(14, \"i\", 11);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(15, MessageLayoutComponent_div_15_Template, 4, 1, \"div\", 12);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(16, \"button\", 13);\n            i0.ɵɵlistener(\"click\", function MessageLayoutComponent_Template_button_click_16_listener() {\n              return ctx.toggleMobileMenu();\n            });\n            i0.ɵɵelement(17, \"i\", 14);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(18, \"div\", 9)(19, \"input\", 15, 16);\n            i0.ɵɵlistener(\"ngModelChange\", function MessageLayoutComponent_Template_input_ngModelChange_19_listener($event) {\n              return ctx.searchQuery = $event;\n            })(\"input\", function MessageLayoutComponent_Template_input_input_19_listener($event) {\n              return ctx.onSearchInput($event);\n            })(\"focus\", function MessageLayoutComponent_Template_input_focus_19_listener() {\n              return ctx.showSearchFilters = true;\n            })(\"blur\", function MessageLayoutComponent_Template_input_blur_19_listener() {\n              return ctx.hideSearchFiltersDelayed();\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(21, \"i\", 17);\n            i0.ɵɵelementStart(22, \"div\", 18);\n            i0.ɵɵtemplate(23, MessageLayoutComponent_button_23_Template, 2, 0, \"button\", 19);\n            i0.ɵɵtemplate(24, MessageLayoutComponent_button_24_Template, 2, 0, \"button\", 20);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(25, MessageLayoutComponent_div_25_Template, 4, 2, \"div\", 21);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(26, \"div\", 22)(27, \"button\", 23);\n            i0.ɵɵlistener(\"click\", function MessageLayoutComponent_Template_button_click_27_listener() {\n              return ctx.switchTab(\"conversations\");\n            });\n            i0.ɵɵelement(28, \"i\", 24);\n            i0.ɵɵelementStart(29, \"div\", 25);\n            i0.ɵɵtext(30, \"Discussions\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(31, \"button\", 23);\n            i0.ɵɵlistener(\"click\", function MessageLayoutComponent_Template_button_click_31_listener() {\n              return ctx.switchTab(\"users\");\n            });\n            i0.ɵɵelement(32, \"i\", 26);\n            i0.ɵɵelementStart(33, \"div\", 25);\n            i0.ɵɵtext(34, \"Contacts\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(35, \"button\", 27);\n            i0.ɵɵlistener(\"click\", function MessageLayoutComponent_Template_button_click_35_listener() {\n              return ctx.switchTab(\"notifications\");\n            });\n            i0.ɵɵelement(36, \"i\", 28);\n            i0.ɵɵelementStart(37, \"div\", 25);\n            i0.ɵɵtext(38, \"Notifications\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(39, MessageLayoutComponent_span_39_Template, 2, 1, \"span\", 29);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(40, \"div\", 30);\n            i0.ɵɵtemplate(41, MessageLayoutComponent_div_41_Template, 4, 3, \"div\", 31);\n            i0.ɵɵtemplate(42, MessageLayoutComponent_div_42_Template, 4, 3, \"div\", 32);\n            i0.ɵɵtemplate(43, MessageLayoutComponent_div_43_Template, 4, 4, \"div\", 33);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(44, \"div\", 34)(45, \"div\", 35)(46, \"button\", 36);\n            i0.ɵɵlistener(\"click\", function MessageLayoutComponent_Template_button_click_46_listener() {\n              return ctx.toggleMobileMenu();\n            });\n            i0.ɵɵelement(47, \"i\", 37);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(48, \"div\", 38);\n            i0.ɵɵelement(49, \"router-outlet\");\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(1);\n            i0.ɵɵclassProp(\"hidden\", !ctx.isMobileMenuOpen)(\"md:flex\", true);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"src\", (ctx.currentUser == null ? null : ctx.currentUser.image) || \"/assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", ctx.currentUser == null ? null : ctx.currentUser.username);\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate1(\" \", ctx.currentUser == null ? null : ctx.currentUser.username, \" \");\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"ngIf\", ctx.showThemeSelector);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngModel\", ctx.searchQuery);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngIf\", ctx.searchQuery);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.searchQuery);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.showSearchFilters && ctx.searchQuery);\n            i0.ɵɵadvance(2);\n            i0.ɵɵclassProp(\"active\", ctx.activeTab === \"conversations\")(\"text-blue-400\", ctx.activeTab === \"conversations\")(\"border-b-2\", ctx.activeTab === \"conversations\")(\"border-blue-500\", ctx.activeTab === \"conversations\")(\"text-gray-400\", ctx.activeTab !== \"conversations\");\n            i0.ɵɵadvance(4);\n            i0.ɵɵclassProp(\"active\", ctx.activeTab === \"users\")(\"text-blue-400\", ctx.activeTab === \"users\")(\"border-b-2\", ctx.activeTab === \"users\")(\"border-blue-500\", ctx.activeTab === \"users\")(\"text-gray-400\", ctx.activeTab !== \"users\");\n            i0.ɵɵadvance(4);\n            i0.ɵɵclassProp(\"active\", ctx.activeTab === \"notifications\")(\"text-blue-400\", ctx.activeTab === \"notifications\")(\"border-b-2\", ctx.activeTab === \"notifications\")(\"border-blue-500\", ctx.activeTab === \"notifications\")(\"text-gray-400\", ctx.activeTab !== \"notifications\");\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngIf\", ctx.notifications.length > 0);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"conversations\");\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"users\");\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"notifications\");\n          }\n        },\n        dependencies: [i7.NgForOf, i7.NgIf, i6.RouterOutlet, i8.DefaultValueAccessor, i8.NgControlStatus, i8.NgModel],\n        styles: [\".message-layout[_ngcontent-%COMP%]{height:100vh;--tw-bg-opacity: 1;background-color:rgb(17 24 39 / var(--tw-bg-opacity, 1));--tw-text-opacity: 1;color:rgb(255 255 255 / var(--tw-text-opacity, 1));font-family:Inter,-apple-system,BlinkMacSystemFont,sans-serif}.sidebar[_ngcontent-%COMP%]{display:flex;width:20rem;flex-direction:column;border-right-width:1px;--tw-border-opacity: 1;border-color:rgb(55 65 81 / var(--tw-border-opacity, 1));--tw-bg-opacity: 1;background-color:rgb(31 41 55 / var(--tw-bg-opacity, 1));background:linear-gradient(180deg,#1f2937 0%,#111827 100%);box-shadow:2px 0 10px #0000004d}.sidebar-header[_ngcontent-%COMP%]{border-bottom-width:1px;--tw-border-opacity: 1;border-color:rgb(55 65 81 / var(--tw-border-opacity, 1));--tw-bg-opacity: 1;background-color:rgb(31 41 55 / var(--tw-bg-opacity, 1));padding:1rem;background:linear-gradient(135deg,#1f2937 0%,#111827 100%)}.tabs[_ngcontent-%COMP%]{display:flex;border-bottom-width:1px;--tw-border-opacity: 1;border-color:rgb(55 65 81 / var(--tw-border-opacity, 1));background:linear-gradient(135deg,#374151 0%,#1f2937 100%)}.tab[_ngcontent-%COMP%]{flex:1 1 0%;cursor:pointer;padding:.75rem 1rem;text-align:center;transition-property:all;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.2s;position:relative}.tab[_ngcontent-%COMP%]:hover{--tw-bg-opacity: 1;background-color:rgb(55 65 81 / var(--tw-bg-opacity, 1))}.conversation-item.tab[_ngcontent-%COMP%]:hover{border-left-width:4px;--tw-border-opacity: 1;border-color:rgb(59 130 246 / var(--tw-border-opacity, 1));background:linear-gradient(135deg,#1e3a8a 0%,#1d4ed8 100%);box-shadow:0 2px 10px #3b82f633}.tab.active[_ngcontent-%COMP%]{border-bottom-width:2px;--tw-border-opacity: 1;border-color:rgb(59 130 246 / var(--tw-border-opacity, 1));--tw-text-opacity: 1;color:rgb(96 165 250 / var(--tw-text-opacity, 1));background:linear-gradient(135deg,#1e3a8a 0%,#1d4ed8 100%);box-shadow:0 2px 10px #3b82f64d}.tab[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{margin-bottom:.25rem;display:block;font-size:1.125rem;line-height:1.75rem}.sidebar-content[_ngcontent-%COMP%]{flex:1 1 0%;overflow-y:auto;scrollbar-width:thin;scrollbar-color:#374151 #1f2937}.sidebar-content[_ngcontent-%COMP%]::-webkit-scrollbar{width:6px}.sidebar-content[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:#1f2937}.sidebar-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#374151;border-radius:3px}.sidebar-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#4b5563}.conversation-item[_ngcontent-%COMP%], .user-item[_ngcontent-%COMP%], .notification-item[_ngcontent-%COMP%]{cursor:pointer;border-bottom-width:1px;--tw-border-opacity: 1;border-color:rgb(55 65 81 / var(--tw-border-opacity, 1));padding:1rem;transition-property:all;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.2s}.conversation-item[_ngcontent-%COMP%]:hover, .user-item[_ngcontent-%COMP%]:hover, .notification-item[_ngcontent-%COMP%]:hover{--tw-bg-opacity: 1;background-color:rgb(55 65 81 / var(--tw-bg-opacity, 1))}.conversation-item[_ngcontent-%COMP%], .user-item[_ngcontent-%COMP%], .notification-item[_ngcontent-%COMP%]{position:relative}.conversation-item[_ngcontent-%COMP%]:hover, .user-item[_ngcontent-%COMP%]:hover, .notification-item[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#374151 0%,#1f2937 100%);transform:translate(2px)}.conversation-item.bg-gray-700[_ngcontent-%COMP%]{border-left-width:4px;--tw-border-opacity: 1;border-color:rgb(59 130 246 / var(--tw-border-opacity, 1));background:linear-gradient(135deg,#1e3a8a 0%,#1d4ed8 100%);box-shadow:0 2px 10px #3b82f633}.user-avatar[_ngcontent-%COMP%], .conversation-avatar[_ngcontent-%COMP%]{height:3rem;width:3rem;border-radius:9999px;border-width:2px;--tw-border-opacity: 1;border-color:rgb(75 85 99 / var(--tw-border-opacity, 1));transition:all .2s ease}.user-avatar[_ngcontent-%COMP%]:hover, .conversation-avatar[_ngcontent-%COMP%]:hover{--tw-border-opacity: 1;border-color:rgb(59 130 246 / var(--tw-border-opacity, 1));box-shadow:0 0 15px #3b82f666}.online-indicator[_ngcontent-%COMP%]{position:absolute;bottom:0;right:0;height:.75rem;width:.75rem;border-radius:9999px;border-width:2px;--tw-border-opacity: 1;border-color:rgb(31 41 55 / var(--tw-border-opacity, 1));--tw-bg-opacity: 1;background-color:rgb(34 197 94 / var(--tw-bg-opacity, 1));animation:_ngcontent-%COMP%_pulse 2s infinite}@keyframes _ngcontent-%COMP%_pulse{0%,to{opacity:1}50%{opacity:.7}}.unread-badge[_ngcontent-%COMP%]{min-width:20px;border-radius:9999px;--tw-bg-opacity: 1;background-color:rgb(59 130 246 / var(--tw-bg-opacity, 1));padding:.25rem .5rem;text-align:center;font-size:.75rem;line-height:1rem;--tw-text-opacity: 1;color:rgb(255 255 255 / var(--tw-text-opacity, 1));background:linear-gradient(135deg,#3b82f6 0%,#1d4ed8 100%);box-shadow:0 2px 8px #3b82f666;animation:_ngcontent-%COMP%_badgePulse 2s infinite}@keyframes _ngcontent-%COMP%_badgePulse{0%,to{transform:scale(1)}50%{transform:scale(1.05)}}.notification-badge[_ngcontent-%COMP%]{position:absolute;top:-.25rem;right:-.25rem;display:flex;height:1.25rem;width:1.25rem;align-items:center;justify-content:center;border-radius:9999px;--tw-bg-opacity: 1;background-color:rgb(239 68 68 / var(--tw-bg-opacity, 1));font-size:.75rem;line-height:1rem;--tw-text-opacity: 1;color:rgb(255 255 255 / var(--tw-text-opacity, 1));background:linear-gradient(135deg,#ef4444 0%,#dc2626 100%);box-shadow:0 2px 8px #ef444466;animation:_ngcontent-%COMP%_notificationPulse 1s infinite}@keyframes _ngcontent-%COMP%_notificationPulse{0%,to{transform:scale(1);opacity:1}50%{transform:scale(1.1);opacity:.8}}.main-content[_ngcontent-%COMP%]{display:flex;flex:1 1 0%;flex-direction:column;background:linear-gradient(180deg,#0f172a 0%,#111827 100%)}@media (max-width: 768px){.sidebar[_ngcontent-%COMP%]{position:fixed;top:0;bottom:0;left:0;z-index:50;width:20rem;transform:translate(-100%);transition:transform .3s ease-in-out}.sidebar.show[_ngcontent-%COMP%]{transform:translate(0)}.main-content[_ngcontent-%COMP%]{width:100%}}\"]\n      });\n    }\n  }\n  return MessageLayoutComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}