{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport let HighlightPresencePipe = /*#__PURE__*/(() => {\n  class HighlightPresencePipe {\n    transform(value, searchTerm) {\n      if (!value || !searchTerm) {\n        return value;\n      }\n      const regex = new RegExp(`(${searchTerm})`, 'gi');\n      return value.replace(regex, '<mark class=\"bg-yellow-200 text-yellow-800\">$1</mark>');\n    }\n    static {\n      this.ɵfac = function HighlightPresencePipe_Factory(t) {\n        return new (t || HighlightPresencePipe)();\n      };\n    }\n    static {\n      this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n        name: \"highlightPresence\",\n        type: HighlightPresencePipe,\n        pure: true\n      });\n    }\n  }\n  return HighlightPresencePipe;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}