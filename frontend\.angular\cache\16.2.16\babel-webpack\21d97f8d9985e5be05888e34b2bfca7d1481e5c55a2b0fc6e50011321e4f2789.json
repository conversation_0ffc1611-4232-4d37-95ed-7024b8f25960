{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule, DatePipe } from '@angular/common';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport { HttpClientModule } from '@angular/common/http';\nimport { SharedComponentsModule } from './components/shared-components.module';\n// AiChatbotModule sera importé directement dans AppModule\nexport let SharedModule = class SharedModule {};\nSharedModule = __decorate([NgModule({\n  declarations: [],\n  imports: [CommonModule, ReactiveFormsModule, FormsModule, RouterModule, HttpClientModule, SharedComponentsModule],\n  providers: [DatePipe],\n  exports: [CommonModule, ReactiveFormsModule, FormsModule, RouterModule, SharedComponentsModule]\n})], SharedModule);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}