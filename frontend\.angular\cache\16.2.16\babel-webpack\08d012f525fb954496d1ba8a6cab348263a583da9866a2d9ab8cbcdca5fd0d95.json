{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport const THEMES = {\n  dark: {\n    name: 'dark',\n    displayName: 'Sombre',\n    colors: {\n      primary: '#3b82f6',\n      secondary: '#6366f1',\n      accent: '#8b5cf6',\n      background: '#111827',\n      surface: '#1f2937',\n      text: '#ffffff',\n      textSecondary: '#9ca3af',\n      border: '#374151',\n      success: '#10b981',\n      warning: '#f59e0b',\n      error: '#ef4444'\n    },\n    gradients: {\n      primary: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',\n      secondary: 'linear-gradient(135deg, #6366f1 0%, #4f46e5 100%)',\n      accent: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)'\n    }\n  },\n  neon: {\n    name: 'neon',\n    displayName: 'Néon',\n    colors: {\n      primary: '#00ffff',\n      secondary: '#ff00ff',\n      accent: '#ffff00',\n      background: '#0a0a0a',\n      surface: '#1a1a1a',\n      text: '#ffffff',\n      textSecondary: '#cccccc',\n      border: '#333333',\n      success: '#00ff00',\n      warning: '#ff8800',\n      error: '#ff0040'\n    },\n    gradients: {\n      primary: 'linear-gradient(135deg, #00ffff 0%, #0080ff 100%)',\n      secondary: 'linear-gradient(135deg, #ff00ff 0%, #8000ff 100%)',\n      accent: 'linear-gradient(135deg, #ffff00 0%, #ff8000 100%)'\n    }\n  },\n  purple: {\n    name: 'purple',\n    displayName: 'Violet',\n    colors: {\n      primary: '#8b5cf6',\n      secondary: '#a855f7',\n      accent: '#c084fc',\n      background: '#1e1b4b',\n      surface: '#312e81',\n      text: '#ffffff',\n      textSecondary: '#c7d2fe',\n      border: '#4c1d95',\n      success: '#22c55e',\n      warning: '#eab308',\n      error: '#ef4444'\n    },\n    gradients: {\n      primary: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',\n      secondary: 'linear-gradient(135deg, #a855f7 0%, #9333ea 100%)',\n      accent: 'linear-gradient(135deg, #c084fc 0%, #a855f7 100%)'\n    }\n  },\n  ocean: {\n    name: 'ocean',\n    displayName: 'Océan',\n    colors: {\n      primary: '#0ea5e9',\n      secondary: '#06b6d4',\n      accent: '#22d3ee',\n      background: '#0c4a6e',\n      surface: '#075985',\n      text: '#ffffff',\n      textSecondary: '#bae6fd',\n      border: '#0369a1',\n      success: '#059669',\n      warning: '#d97706',\n      error: '#dc2626'\n    },\n    gradients: {\n      primary: 'linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%)',\n      secondary: 'linear-gradient(135deg, #06b6d4 0%, #0891b2 100%)',\n      accent: 'linear-gradient(135deg, #22d3ee 0%, #06b6d4 100%)'\n    }\n  }\n};\nexport let ThemeService = /*#__PURE__*/(() => {\n  class ThemeService {\n    constructor() {\n      this.currentTheme = new BehaviorSubject(THEMES['dark']);\n      this.currentTheme$ = this.currentTheme.asObservable();\n      this.loadThemeFromStorage();\n      this.applyTheme(this.currentTheme.value);\n    }\n    setTheme(themeName) {\n      const theme = THEMES[themeName];\n      if (theme) {\n        this.currentTheme.next(theme);\n        this.applyTheme(theme);\n        this.saveThemeToStorage(themeName);\n      }\n    }\n    getCurrentTheme() {\n      return this.currentTheme.value;\n    }\n    getAvailableThemes() {\n      return Object.values(THEMES);\n    }\n    applyTheme(theme) {\n      const root = document.documentElement;\n      // Appliquer les couleurs CSS custom properties\n      Object.entries(theme.colors).forEach(([key, value]) => {\n        root.style.setProperty(`--color-${key}`, value);\n      });\n      // Appliquer les gradients\n      Object.entries(theme.gradients).forEach(([key, value]) => {\n        root.style.setProperty(`--gradient-${key}`, value);\n      });\n      // Ajouter la classe de thème au body\n      document.body.className = document.body.className.replace(/theme-\\w+/g, '');\n      document.body.classList.add(`theme-${theme.name}`);\n    }\n    saveThemeToStorage(themeName) {\n      try {\n        localStorage.setItem('selectedTheme', themeName);\n      } catch (error) {\n        console.warn('Could not save theme to localStorage:', error);\n      }\n    }\n    loadThemeFromStorage() {\n      try {\n        const savedTheme = localStorage.getItem('selectedTheme');\n        if (savedTheme && THEMES[savedTheme]) {\n          this.currentTheme.next(THEMES[savedTheme]);\n        }\n      } catch (error) {\n        console.warn('Could not load theme from localStorage:', error);\n      }\n    }\n    // Méthodes utilitaires pour les composants\n    getPrimaryColor() {\n      return this.currentTheme.value.colors.primary;\n    }\n    getSecondaryColor() {\n      return this.currentTheme.value.colors.secondary;\n    }\n    getAccentColor() {\n      return this.currentTheme.value.colors.accent;\n    }\n    getPrimaryGradient() {\n      return this.currentTheme.value.gradients.primary;\n    }\n    isTheme(themeName) {\n      return this.currentTheme.value.name === themeName;\n    }\n    // Méthode pour basculer entre les thèmes\n    toggleTheme() {\n      const currentTheme = this.currentTheme.value;\n      if (currentTheme.name === 'dark') {\n        this.setTheme('light');\n      } else {\n        this.setTheme('dark');\n      }\n    }\n    // Alias pour la compatibilité\n    toggleDarkMode() {\n      this.toggleTheme();\n    }\n    static {\n      this.ɵfac = function ThemeService_Factory(t) {\n        return new (t || ThemeService)();\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: ThemeService,\n        factory: ThemeService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return ThemeService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}