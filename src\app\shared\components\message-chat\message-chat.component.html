<!-- Interface de chat principal -->
<div class="message-chat-container">
  
  <!-- Sidebar des conversations -->
  <div class="conversations-sidebar">
    
    <!-- En-tête de la sidebar -->
    <div class="sidebar-header">
      <h3 class="sidebar-title">
        <i class="fas fa-comments"></i>
        Messages
      </h3>
      
      <!-- Recherche -->
      <div class="search-container">
        <input
          type="text"
          [(ngModel)]="searchQuery"
          placeholder="Rechercher une conversation..."
          class="search-input">
        <i class="fas fa-search search-icon"></i>
      </div>
    </div>
    
    <!-- Liste des conversations -->
    <div class="conversations-list">
      <div
        *ngFor="let conversation of conversations"
        class="conversation-item"
        [class.active]="conversation.id === selectedConversationId"
        [class.unread]="conversation.unreadCount > 0"
        (click)="selectConversation(conversation.id)">
        
        <!-- Avatar -->
        <div class="conversation-avatar">
          <img [src]="getConversationAvatar(conversation)" [alt]="conversation.name">
          <div 
            class="online-indicator"
            [class.online]="conversation.isOnline"
            *ngIf="!conversation.isGroup">
          </div>
        </div>
        
        <!-- Informations -->
        <div class="conversation-info">
          <div class="conversation-header">
            <h4 class="conversation-name">{{ conversation.name }}</h4>
            <span class="conversation-time" *ngIf="conversation.lastMessage">
              {{ getMessageTime(conversation.lastMessage.timestamp) }}
            </span>
          </div>
          
          <div class="conversation-preview">
            <p class="last-message">{{ getLastMessagePreview(conversation) }}</p>
            <div class="conversation-badges">
              <span 
                *ngIf="conversation.unreadCount > 0"
                class="unread-badge">
                {{ conversation.unreadCount }}
              </span>
              <i 
                *ngIf="conversation.isTyping"
                class="fas fa-ellipsis-h typing-indicator">
              </i>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Zone de chat principale -->
  <div class="chat-main" *ngIf="selectedConversation">
    
    <!-- En-tête du chat -->
    <div class="chat-header">
      <div class="chat-user-info">
        <img 
          [src]="getConversationAvatar(selectedConversation)" 
          [alt]="selectedConversation.name"
          class="chat-avatar">
        
        <div class="chat-user-details">
          <h3 class="chat-user-name">{{ selectedConversation.name }}</h3>
          <p class="chat-user-status">
            <span *ngIf="selectedConversation.isOnline" class="status-online">
              <i class="fas fa-circle"></i>
              En ligne
            </span>
            <span *ngIf="!selectedConversation.isOnline && selectedConversation.lastSeen" class="status-offline">
              Vu {{ selectedConversation.lastSeen | date:'short' }}
            </span>
            <span *ngIf="selectedConversation.isTyping" class="status-typing">
              <i class="fas fa-ellipsis-h"></i>
              En train d'écrire...
            </span>
          </p>
        </div>
      </div>
      
      <!-- Actions du chat -->
      <div class="chat-actions">
        <button class="action-btn" title="Appel vocal">
          <i class="fas fa-phone"></i>
        </button>
        <button class="action-btn" title="Appel vidéo">
          <i class="fas fa-video"></i>
        </button>
        <button class="action-btn" title="Informations">
          <i class="fas fa-info-circle"></i>
        </button>
      </div>
    </div>
    
    <!-- Zone des messages -->
    <div class="messages-container" #messagesContainer>
      
      <!-- Indicateur de chargement -->
      <div *ngIf="isLoading" class="loading-indicator">
        <i class="fas fa-spinner fa-spin"></i>
        Chargement des messages...
      </div>
      
      <!-- Messages -->
      <div class="messages-list">
        
        <!-- Message de réponse (si applicable) -->
        <div *ngIf="replyToMessage" class="reply-preview">
          <div class="reply-content">
            <i class="fas fa-reply"></i>
            <span class="reply-text">Réponse à: {{ replyToMessage.content.substring(0, 50) }}...</span>
          </div>
          <button (click)="replyToMessage = undefined" class="reply-cancel">
            <i class="fas fa-times"></i>
          </button>
        </div>
        
        <!-- Liste des messages -->
        <div
          *ngFor="let message of messages; trackBy: trackByMessageId"
          class="message-wrapper"
          [class.my-message]="isMyMessage(message)"
          [class.has-reply]="message.replyTo">
          
          <!-- Message de réponse référencé -->
          <div *ngIf="message.replyTo" class="replied-message">
            <i class="fas fa-reply"></i>
            <span>Réponse à un message</span>
          </div>
          
          <!-- Contenu du message -->
          <div class="message-bubble">
            
            <!-- Contenu textuel -->
            <div *ngIf="message.type === 'text'" class="message-text">
              {{ message.content }}
            </div>
            
            <!-- Contenu image -->
            <div *ngIf="message.type === 'image' && message.attachments" class="message-image">
              <img 
                *ngFor="let attachment of message.attachments"
                [src]="attachment.url" 
                [alt]="attachment.name"
                class="image-attachment">
            </div>
            
            <!-- Contenu fichier -->
            <div *ngIf="message.type === 'file' && message.attachments" class="message-file">
              <div *ngFor="let attachment of message.attachments" class="file-attachment">
                <i class="fas fa-file"></i>
                <div class="file-info">
                  <span class="file-name">{{ attachment.name }}</span>
                  <span class="file-size">{{ (attachment.size / 1024 / 1024).toFixed(2) }} MB</span>
                </div>
                <a [href]="attachment.url" download class="file-download">
                  <i class="fas fa-download"></i>
                </a>
              </div>
            </div>
            
            <!-- Contenu vocal -->
            <div *ngIf="message.type === 'voice'" class="message-voice">
              <app-voice-player 
                [voiceMessage]="{ url: message.attachments?.[0]?.url || '', duration: 0, isPlayed: false }"
                [isMyMessage]="isMyMessage(message)">
              </app-voice-player>
            </div>
            
            <!-- Métadonnées du message -->
            <div class="message-meta">
              <span class="message-time">{{ getMessageTime(message.timestamp) }}</span>
              <i *ngIf="message.isEdited" class="fas fa-edit edited-indicator" title="Message modifié"></i>
              <i *ngIf="isMyMessage(message) && message.isRead" class="fas fa-check-double read-indicator" title="Lu"></i>
              <i *ngIf="isMyMessage(message) && !message.isRead" class="fas fa-check sent-indicator" title="Envoyé"></i>
            </div>
            
            <!-- Réactions -->
            <div *ngIf="message.reactions && message.reactions.length > 0" class="message-reactions">
              <span 
                *ngFor="let reaction of message.reactions"
                class="reaction-item"
                [title]="reaction.userName">
                {{ reaction.emoji }}
              </span>
            </div>
          </div>
          
          <!-- Menu d'actions du message -->
          <div class="message-actions" *ngIf="!isMyMessage(message) || message.senderId === currentUserId">
            <button (click)="replyToMessageAction(message)" class="action-btn" title="Répondre">
              <i class="fas fa-reply"></i>
            </button>
            <button (click)="addReaction(message, '👍')" class="action-btn" title="Réagir">
              <i class="fas fa-smile"></i>
            </button>
            <button *ngIf="isMyMessage(message)" (click)="editMessage(message)" class="action-btn" title="Modifier">
              <i class="fas fa-edit"></i>
            </button>
            <button (click)="forwardMessage(message)" class="action-btn" title="Transférer">
              <i class="fas fa-share"></i>
            </button>
            <button *ngIf="isMyMessage(message)" (click)="deleteMessage(message)" class="action-btn danger" title="Supprimer">
              <i class="fas fa-trash"></i>
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Zone de saisie -->
    <div class="message-input-container">
      
      <!-- Barre d'outils -->
      <div class="input-toolbar">
        <button class="toolbar-btn" (click)="fileInput.click()" title="Joindre un fichier">
          <i class="fas fa-paperclip"></i>
        </button>
        <button class="toolbar-btn" title="Enregistrer un message vocal">
          <i class="fas fa-microphone"></i>
        </button>
        <button class="toolbar-btn" title="Emoji">
          <i class="fas fa-smile"></i>
        </button>
        <button class="toolbar-btn" title="Programmer un message">
          <i class="fas fa-clock"></i>
        </button>
      </div>
      
      <!-- Zone de saisie -->
      <div class="input-area">
        <textarea
          #messageInput
          [(ngModel)]="currentMessage"
          (input)="onMessageInput()"
          (keydown.enter)="!$event.shiftKey && sendMessage(); $event.preventDefault()"
          placeholder="Tapez votre message..."
          class="message-input"
          rows="1">
        </textarea>
        
        <button 
          (click)="sendMessage()"
          [disabled]="!currentMessage.trim()"
          class="send-btn"
          title="Envoyer">
          <i class="fas fa-paper-plane"></i>
        </button>
      </div>
      
      <!-- Input de fichier caché -->
      <input
        #fileInput
        type="file"
        multiple
        (change)="onFileSelected($event)"
        style="display: none"
        accept="image/*,video/*,audio/*,.pdf,.doc,.docx,.txt">
    </div>
  </div>
  
  <!-- Message de sélection -->
  <div class="no-conversation" *ngIf="!selectedConversation">
    <div class="no-conversation-content">
      <i class="fas fa-comments"></i>
      <h3>Sélectionnez une conversation</h3>
      <p>Choisissez une conversation dans la liste pour commencer à discuter.</p>
    </div>
  </div>
</div>
