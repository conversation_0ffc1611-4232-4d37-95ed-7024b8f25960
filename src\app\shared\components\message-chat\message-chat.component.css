/* ============================================================================
   MESSAGE CHAT COMPONENT - INTERFACE DE CHAT PRINCIPALE
   ============================================================================ */

.message-chat-container {
  display: flex;
  height: 100vh;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  color: #ffffff;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* ============================================================================
   SIDEBAR DES CONVERSATIONS
   ============================================================================ */

.conversations-sidebar {
  width: 320px;
  background: linear-gradient(180deg, #1e293b 0%, #0f172a 100%);
  border-right: 1px solid #334155;
  display: flex;
  flex-direction: column;
  backdrop-filter: blur(10px);
}

.sidebar-header {
  padding: 1.5rem;
  border-bottom: 1px solid #334155;
  background: rgba(30, 41, 59, 0.8);
}

.sidebar-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.25rem;
  font-weight: 700;
  color: #ffffff;
  margin: 0 0 1rem 0;
}

.sidebar-title i {
  color: #3b82f6;
  font-size: 1.5rem;
}

.search-container {
  position: relative;
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  background: rgba(15, 23, 42, 0.8);
  border: 1px solid #475569;
  border-radius: 0.75rem;
  color: #ffffff;
  font-size: 0.875rem;
  transition: all 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-input::placeholder {
  color: #64748b;
}

.search-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #64748b;
  font-size: 0.875rem;
}

.conversations-list {
  flex: 1;
  overflow-y: auto;
  padding: 0.5rem;
}

.conversation-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.875rem;
  border-radius: 0.75rem;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 0.25rem;
  position: relative;
}

.conversation-item:hover {
  background: rgba(59, 130, 246, 0.1);
  transform: translateX(4px);
}

.conversation-item.active {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.3);
}

.conversation-item.unread {
  background: rgba(16, 185, 129, 0.05);
  border-left: 3px solid #10b981;
}

.conversation-avatar {
  position: relative;
  flex-shrink: 0;
}

.conversation-avatar img {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid rgba(255, 255, 255, 0.1);
}

.online-indicator {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 0.875rem;
  height: 0.875rem;
  border-radius: 50%;
  background: #64748b;
  border: 2px solid #1e293b;
}

.online-indicator.online {
  background: #10b981;
  box-shadow: 0 0 8px rgba(16, 185, 129, 0.5);
}

.conversation-info {
  flex: 1;
  min-width: 0;
}

.conversation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.25rem;
}

.conversation-name {
  font-size: 0.875rem;
  font-weight: 600;
  color: #ffffff;
  margin: 0;
  truncate: true;
}

.conversation-time {
  font-size: 0.75rem;
  color: #94a3b8;
}

.conversation-preview {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.last-message {
  font-size: 0.8rem;
  color: #94a3b8;
  margin: 0;
  truncate: true;
  max-width: 180px;
}

.conversation-badges {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.unread-badge {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: #ffffff;
  font-size: 0.625rem;
  font-weight: 700;
  padding: 0.125rem 0.375rem;
  border-radius: 1rem;
  min-width: 1.25rem;
  text-align: center;
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
}

.typing-indicator {
  color: #3b82f6;
  animation: pulse 1.5s ease-in-out infinite;
}

/* ============================================================================
   ZONE DE CHAT PRINCIPALE
   ============================================================================ */

.chat-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: linear-gradient(180deg, #0f172a 0%, #1e293b 100%);
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background: rgba(30, 41, 59, 0.8);
  border-bottom: 1px solid #334155;
  backdrop-filter: blur(10px);
}

.chat-user-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.chat-avatar {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid rgba(255, 255, 255, 0.1);
}

.chat-user-details h3 {
  font-size: 1rem;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 0.25rem 0;
}

.chat-user-status {
  font-size: 0.75rem;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.375rem;
}

.status-online {
  color: #10b981;
}

.status-offline {
  color: #94a3b8;
}

.status-typing {
  color: #3b82f6;
  animation: pulse 1.5s ease-in-out infinite;
}

.status-online i {
  font-size: 0.5rem;
}

.chat-actions {
  display: flex;
  gap: 0.5rem;
}

.action-btn {
  width: 2.5rem;
  height: 2.5rem;
  border: none;
  background: rgba(255, 255, 255, 0.1);
  color: #94a3b8;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.action-btn:hover {
  background: rgba(59, 130, 246, 0.2);
  color: #3b82f6;
  transform: scale(1.1);
}

.action-btn.danger:hover {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

/* ============================================================================
   ZONE DES MESSAGES
   ============================================================================ */

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  background: linear-gradient(180deg, rgba(15, 23, 42, 0.5) 0%, rgba(30, 41, 59, 0.5) 100%);
}

.loading-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 2rem;
  color: #94a3b8;
  font-size: 0.875rem;
}

.messages-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.reply-preview {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid #3b82f6;
  border-radius: 0.5rem;
  padding: 0.75rem;
  margin-bottom: 1rem;
}

.reply-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #3b82f6;
  font-size: 0.875rem;
}

.reply-cancel {
  background: none;
  border: none;
  color: #94a3b8;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 0.25rem;
  transition: all 0.2s ease;
}

.reply-cancel:hover {
  color: #ef4444;
  background: rgba(239, 68, 68, 0.1);
}

.message-wrapper {
  display: flex;
  flex-direction: column;
  max-width: 70%;
  position: relative;
}

.message-wrapper.my-message {
  align-self: flex-end;
  align-items: flex-end;
}

.replied-message {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  font-size: 0.75rem;
  color: #94a3b8;
  margin-bottom: 0.25rem;
  padding: 0.25rem 0.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 0.375rem;
}

.message-bubble {
  background: linear-gradient(135deg, #374151 0%, #1f2937 100%);
  border-radius: 1rem;
  padding: 0.875rem 1rem;
  position: relative;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.message-wrapper.my-message .message-bubble {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: #ffffff;
}

.message-text {
  font-size: 0.875rem;
  line-height: 1.5;
  word-wrap: break-word;
  margin-bottom: 0.5rem;
}

.message-image img {
  max-width: 100%;
  border-radius: 0.5rem;
  margin-bottom: 0.5rem;
}

.message-file {
  margin-bottom: 0.5rem;
}

.file-attachment {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 0.5rem;
  padding: 0.75rem;
}

.file-attachment i {
  font-size: 1.5rem;
  color: #3b82f6;
}

.file-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.file-name {
  font-size: 0.875rem;
  font-weight: 500;
  color: #ffffff;
}

.file-size {
  font-size: 0.75rem;
  color: #94a3b8;
}

.file-download {
  color: #3b82f6;
  text-decoration: none;
  padding: 0.5rem;
  border-radius: 0.375rem;
  transition: all 0.2s ease;
}

.file-download:hover {
  background: rgba(59, 130, 246, 0.1);
}

.message-voice {
  margin-bottom: 0.5rem;
}

.message-meta {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.7);
  margin-top: 0.25rem;
}

.message-wrapper.my-message .message-meta {
  color: rgba(255, 255, 255, 0.8);
}

.edited-indicator,
.read-indicator,
.sent-indicator {
  font-size: 0.625rem;
}

.read-indicator {
  color: #10b981;
}

.sent-indicator {
  color: #94a3b8;
}

.message-reactions {
  display: flex;
  gap: 0.25rem;
  margin-top: 0.5rem;
  flex-wrap: wrap;
}

.reaction-item {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 1rem;
  padding: 0.125rem 0.375rem;
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.reaction-item:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

.message-actions {
  display: flex;
  gap: 0.25rem;
  margin-top: 0.5rem;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.message-wrapper:hover .message-actions {
  opacity: 1;
}

.message-actions .action-btn {
  width: 1.75rem;
  height: 1.75rem;
  font-size: 0.75rem;
}

/* ============================================================================
   ZONE DE SAISIE
   ============================================================================ */

.message-input-container {
  background: rgba(30, 41, 59, 0.8);
  border-top: 1px solid #334155;
  padding: 1rem 1.5rem;
  backdrop-filter: blur(10px);
}

.input-toolbar {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.toolbar-btn {
  width: 2rem;
  height: 2rem;
  border: none;
  background: rgba(255, 255, 255, 0.1);
  color: #94a3b8;
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.875rem;
}

.toolbar-btn:hover {
  background: rgba(59, 130, 246, 0.2);
  color: #3b82f6;
  transform: scale(1.1);
}

.input-area {
  display: flex;
  gap: 0.75rem;
  align-items: flex-end;
}

.message-input {
  flex: 1;
  background: rgba(15, 23, 42, 0.8);
  border: 1px solid #475569;
  border-radius: 1rem;
  padding: 0.75rem 1rem;
  color: #ffffff;
  font-size: 0.875rem;
  line-height: 1.5;
  resize: none;
  max-height: 120px;
  transition: all 0.3s ease;
}

.message-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.message-input::placeholder {
  color: #64748b;
}

.send-btn {
  width: 2.75rem;
  height: 2.75rem;
  border: none;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: #ffffff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.3);
}

.send-btn:hover:not(:disabled) {
  transform: scale(1.1);
  box-shadow: 0 6px 25px rgba(59, 130, 246, 0.4);
}

.send-btn:disabled {
  background: #475569;
  cursor: not-allowed;
  box-shadow: none;
}

/* ============================================================================
   MESSAGE DE SÉLECTION
   ============================================================================ */

.no-conversation {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(180deg, #0f172a 0%, #1e293b 100%);
}

.no-conversation-content {
  text-align: center;
  color: #94a3b8;
}

.no-conversation-content i {
  font-size: 4rem;
  color: #475569;
  margin-bottom: 1rem;
}

.no-conversation-content h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 0.5rem 0;
}

.no-conversation-content p {
  font-size: 0.875rem;
  margin: 0;
}

/* ============================================================================
   ANIMATIONS
   ============================================================================ */

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* ============================================================================
   RESPONSIVE
   ============================================================================ */

@media (max-width: 768px) {
  .message-chat-container {
    flex-direction: column;
  }
  
  .conversations-sidebar {
    width: 100%;
    height: 40vh;
  }
  
  .chat-main {
    height: 60vh;
  }
  
  .message-wrapper {
    max-width: 85%;
  }
  
  .chat-header {
    padding: 0.75rem 1rem;
  }
  
  .messages-container {
    padding: 0.75rem;
  }
  
  .message-input-container {
    padding: 0.75rem 1rem;
  }
}
