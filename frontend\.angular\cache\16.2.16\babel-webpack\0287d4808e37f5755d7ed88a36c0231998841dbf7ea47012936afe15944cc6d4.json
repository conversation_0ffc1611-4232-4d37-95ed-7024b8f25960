{"ast": null, "code": "import { NavigationEnd } from '@angular/router';\nimport { trigger, style, animate, transition, query, stagger, keyframes, state } from '@angular/animations';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/planning.service\";\nimport * as i2 from \"src/app/services/authuser.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"src/app/services/toast.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"../../../../shared/pipes/highlight-presence.pipe\";\nfunction PlanningListComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"div\", 13);\n    i0.ɵɵelement(2, \"div\", 14)(3, \"div\", 15);\n    i0.ɵɵelementStart(4, \"div\", 16);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(5, \"svg\", 17);\n    i0.ɵɵelement(6, \"path\", 18);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(7, \"p\", 19);\n    i0.ɵɵtext(8, \"Chargement des plannings...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PlanningListComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 21);\n    i0.ɵɵelement(2, \"path\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(3, \"h3\", 23);\n    i0.ɵɵtext(4, \"Aucun planning disponible\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 24);\n    i0.ɵɵtext(6, \"Cr\\u00E9ez votre premier planning pour commencer \\u00E0 organiser vos r\\u00E9unions.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"a\", 25);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(8, \"svg\", 26);\n    i0.ɵɵelement(9, \"path\", 8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" Cr\\u00E9er un planning \");\n    i0.ɵɵelementEnd()();\n  }\n}\nconst _c0 = function (a0, a1) {\n  return {\n    \"text-gray-800\": a0,\n    \"text-gray-400\": a1\n  };\n};\nfunction PlanningListComponent_div_13_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵlistener(\"mouseenter\", function PlanningListComponent_div_13_div_1_Template_div_mouseenter_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r7);\n      const i_r5 = restoredCtx.index;\n      const ctx_r6 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r6.onMouseEnter(i_r5));\n    })(\"mouseleave\", function PlanningListComponent_div_13_div_1_Template_div_mouseleave_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r8 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r8.onMouseLeave());\n    });\n    i0.ɵɵelementStart(1, \"div\", 30)(2, \"div\")(3, \"h3\", 31)(4, \"a\", 32);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(6, \"p\", 33);\n    i0.ɵɵpipe(7, \"highlightPresence\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function PlanningListComponent_div_13_div_1_Template_button_click_8_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r7);\n      const planning_r4 = restoredCtx.$implicit;\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      ctx_r9.deletePlanning(planning_r4._id);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(9, \"svg\", 35);\n    i0.ɵɵelement(10, \"path\", 36);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(11, \"div\", 37);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(12, \"svg\", 38);\n    i0.ɵɵelement(13, \"path\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"date\");\n    i0.ɵɵpipe(16, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(17, \"div\", 39)(18, \"span\", 40)(19, \"span\", 6);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(20, \"svg\", 41);\n    i0.ɵɵelement(21, \"path\", 18)(22, \"circle\", 42)(23, \"path\", 43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(24, \"strong\");\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(26, \"\\u00A0r\\u00E9union(s) \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"a\", 44);\n    i0.ɵɵlistener(\"click\", function PlanningListComponent_div_13_div_1_Template_a_click_27_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r7);\n      const planning_r4 = restoredCtx.$implicit;\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.GotoDetail(planning_r4._id));\n    });\n    i0.ɵɵtext(28, \" Voir d\\u00E9tails \\u2192 \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const planning_r4 = ctx.$implicit;\n    const i_r5 = ctx.index;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"@cardHover\", ctx_r3.getCardState(i_r5));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", planning_r4.titre, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"innerHTML\", i0.ɵɵpipeBind1(7, 8, planning_r4.description || \"Aucune description\"), i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind2(15, 10, planning_r4.dateDebut, \"mediumDate\"), \" - \", i0.ɵɵpipeBind2(16, 13, planning_r4.dateFin, \"mediumDate\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(16, _c0, ((planning_r4.reunions == null ? null : planning_r4.reunions.length) || 0) > 0, ((planning_r4.reunions == null ? null : planning_r4.reunions.length) || 0) === 0));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(19, _c0, ((planning_r4.reunions == null ? null : planning_r4.reunions.length) || 0) > 0, ((planning_r4.reunions == null ? null : planning_r4.reunions.length) || 0) === 0));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate((planning_r4.reunions == null ? null : planning_r4.reunions.length) || 0);\n  }\n}\nfunction PlanningListComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵtemplate(1, PlanningListComponent_div_13_div_1_Template, 29, 22, \"div\", 28);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"@staggerAnimation\", ctx_r2.plannings.length);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.plannings)(\"ngForTrackBy\", ctx_r2.trackByFn);\n  }\n}\nexport let PlanningListComponent = /*#__PURE__*/(() => {\n  class PlanningListComponent {\n    constructor(planningService, authService, router, route, toastService) {\n      this.planningService = planningService;\n      this.authService = authService;\n      this.router = router;\n      this.route = route;\n      this.toastService = toastService;\n      this.plannings = [];\n      this.loading = true;\n      this.error = null;\n      this.hoveredIndex = null;\n    }\n    ngOnInit() {\n      console.log('PlanningListComponent initialized');\n      // S'abonner aux événements de navigation pour recharger les plannings\n      this.router.events.subscribe(event => {\n        // NavigationEnd est émis lorsque la navigation est terminée\n        if (event instanceof NavigationEnd) {\n          console.log('Navigation terminée, rechargement des plannings');\n          this.loadPlannings();\n        }\n      });\n      // Chargement initial des plannings\n      this.loadPlannings();\n    }\n    loadPlannings() {\n      this.loading = true;\n      console.log('Loading plannings...');\n      // Utiliser getAllPlannings au lieu de getPlanningsByUser pour afficher tous les plannings\n      this.planningService.getAllPlannings().subscribe({\n        next: response => {\n          console.log('Response received:', response);\n          if (response.success) {\n            // Récupérer les plannings\n            let plannings = response.plannings;\n            // Trier les plannings par nombre de réunions (ordre décroissant)\n            plannings.sort((a, b) => {\n              const reunionsA = a.reunions?.length || 0;\n              const reunionsB = b.reunions?.length || 0;\n              return reunionsB - reunionsA; // Ordre décroissant\n            });\n\n            this.plannings = plannings;\n            console.log('Plannings loaded and sorted by reunion count:', this.plannings.length);\n            if (this.plannings.length > 0) {\n              console.log('First planning:', this.plannings[0]);\n              console.log('Reunion counts:', this.plannings.map(p => ({\n                titre: p.titre,\n                reunions: p.reunions?.length || 0\n              })));\n            }\n          } else {\n            console.error('Error in response:', response);\n            this.toastService.showError('Erreur lors du chargement des plannings');\n          }\n          this.loading = false;\n        },\n        error: err => {\n          console.error('Error loading plannings:', err);\n          this.loading = false;\n          const errorMessage = err.message || err.statusText || 'Erreur inconnue';\n          this.toastService.showError(`Erreur lors du chargement des plannings: ${errorMessage}`);\n        }\n      });\n    }\n    deletePlanning(id) {\n      if (confirm('Supprimer ce planning ?')) {\n        this.planningService.deletePlanning(id).subscribe({\n          next: () => {\n            this.plannings = this.plannings.filter(p => p._id !== id);\n            this.toastService.showSuccess('Le planning a été supprimé avec succès');\n          },\n          error: err => {\n            console.error('Erreur lors de la suppression du planning:', err);\n            // Gestion spécifique des erreurs d'autorisation\n            if (err.status === 403) {\n              this.toastService.showError(\"Accès refusé : vous n'avez pas les droits pour supprimer ce planning\");\n            } else if (err.status === 401) {\n              this.toastService.showError('Vous devez être connecté pour supprimer un planning');\n            } else {\n              const errorMessage = err.error?.message || 'Erreur lors de la suppression du planning';\n              this.toastService.showError(errorMessage, 8000);\n            }\n          }\n        });\n      }\n    }\n    GotoDetail(id) {\n      if (id) {\n        this.router.navigate([id], {\n          relativeTo: this.route\n        });\n      }\n    }\n    // Méthodes pour les animations de survol\n    onMouseEnter(index) {\n      this.hoveredIndex = index;\n    }\n    onMouseLeave() {\n      this.hoveredIndex = null;\n    }\n    getCardState(index) {\n      return this.hoveredIndex === index ? 'hovered' : 'default';\n    }\n    // Méthode pour le suivi des éléments dans ngFor\n    trackByFn(index, planning) {\n      return planning._id || index.toString();\n    }\n    static {\n      this.ɵfac = function PlanningListComponent_Factory(t) {\n        return new (t || PlanningListComponent)(i0.ɵɵdirectiveInject(i1.PlanningService), i0.ɵɵdirectiveInject(i2.AuthuserService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i4.ToastService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: PlanningListComponent,\n        selectors: [[\"app-planning-list\"]],\n        decls: 14,\n        vars: 4,\n        consts: [[1, \"container\", \"mx-auto\", \"px-4\", \"py-6\"], [1, \"flex\", \"justify-between\", \"items-center\", \"mb-6\"], [1, \"text-2xl\", \"font-bold\", \"text-gray-800\", \"relative\", \"planning-header\"], [1, \"bg-clip-text\", \"text-transparent\", \"bg-gradient-to-r\", \"from-purple-600\", \"to-blue-500\"], [1, \"underline-animation\"], [\"routerLink\", \"/plannings/nouveau\", 1, \"px-4\", \"py-2\", \"bg-gradient-to-r\", \"from-purple-600\", \"to-blue-500\", \"text-white\", \"rounded-md\", \"hover:from-purple-700\", \"hover:to-blue-600\", \"transition-all\", \"duration-300\", \"transform\", \"hover:scale-105\", \"hover:shadow-lg\", \"add-button\"], [1, \"flex\", \"items-center\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-5\", \"w-5\", \"mr-1\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 6v6m0 0v6m0-6h6m-6 0H6\"], [\"class\", \"text-center py-12\", 4, \"ngIf\"], [\"class\", \"text-center py-12 bg-white rounded-lg shadow-md\", 4, \"ngIf\"], [\"class\", \"grid gap-4 md:grid-cols-2 lg:grid-cols-3\", 4, \"ngIf\"], [1, \"text-center\", \"py-12\"], [1, \"relative\", \"mx-auto\", \"w-20\", \"h-20\"], [1, \"absolute\", \"top-0\", \"left-0\", \"w-full\", \"h-full\", \"border-4\", \"border-purple-200\", \"rounded-full\"], [1, \"absolute\", \"top-0\", \"left-0\", \"w-full\", \"h-full\", \"border-4\", \"border-transparent\", \"border-t-purple-600\", \"rounded-full\", \"animate-spin\"], [1, \"absolute\", \"top-1/2\", \"left-1/2\", \"transform\", \"-translate-x-1/2\", \"-translate-y-1/2\", \"text-purple-600\", \"font-semibold\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-8\", \"w-8\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"], [1, \"mt-4\", \"text-gray-600\", \"animate-pulse\"], [1, \"text-center\", \"py-12\", \"bg-white\", \"rounded-lg\", \"shadow-md\"], [\"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"mx-auto\", \"h-16\", \"w-16\", \"text-purple-300\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\"], [1, \"mt-4\", \"text-xl\", \"font-medium\", \"text-gray-900\"], [1, \"mt-2\", \"text-gray-600\"], [\"routerLink\", \"/plannings/nouveau\", 1, \"mt-6\", \"inline-flex\", \"items-center\", \"px-4\", \"py-2\", \"bg-purple-600\", \"text-white\", \"rounded-md\", \"hover:bg-purple-700\", \"transition-all\", \"duration-300\", \"transform\", \"hover:scale-105\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-5\", \"w-5\", \"mr-2\"], [1, \"grid\", \"gap-4\", \"md:grid-cols-2\", \"lg:grid-cols-3\"], [\"class\", \"bg-white rounded-lg shadow-md p-4 cursor-pointer transform transition-all duration-300 relative\", 3, \"mouseenter\", \"mouseleave\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"bg-white\", \"rounded-lg\", \"shadow-md\", \"p-4\", \"cursor-pointer\", \"transform\", \"transition-all\", \"duration-300\", \"relative\", 3, \"mouseenter\", \"mouseleave\"], [1, \"flex\", \"justify-between\", \"items-start\"], [1, \"text-lg\", \"font-semibold\", \"text-gray-800\"], [1, \"hover:text-purple-600\", \"planning-title\"], [1, \"text-sm\", \"mt-1\", 3, \"innerHTML\"], [1, \"text-red-500\", \"hover:text-red-700\", \"transition-colors\", \"duration-300\", 3, \"click\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-5\", \"w-5\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"], [1, \"mt-3\", \"flex\", \"items-center\", \"text-sm\", \"text-purple-700\", \"font-medium\"], [\"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-4\", \"w-4\", \"mr-1\", \"text-purple-700\"], [1, \"mt-4\", \"pt-3\", \"border-t\", \"border-gray-100\", \"flex\", \"justify-between\", \"items-center\"], [1, \"text-sm\", \"font-medium\", \"reunion-count\", 3, \"ngClass\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", 1, \"h-4\", \"w-4\", \"mr-1\", 3, \"ngClass\"], [\"cx\", \"12\", \"cy\", \"14\", \"r\", \"3\", \"stroke-width\", \"1.5\"], [\"stroke-linecap\", \"round\", \"stroke-width\", \"1.5\", \"d\", \"M12 12v2h2\"], [1, \"text-sm\", \"hover:text-purple-900\", \"font-medium\", \"details-link\", 2, \"color\", \"#6b46c1 !important\", 3, \"click\"]],\n        template: function PlanningListComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\", 2)(3, \"span\", 3);\n            i0.ɵɵtext(4, \"Mes Plannings\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(5, \"span\", 4);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"a\", 5)(7, \"span\", 6);\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(8, \"svg\", 7);\n            i0.ɵɵelement(9, \"path\", 8);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(10, \" Nouveau Planning \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(11, PlanningListComponent_div_11_Template, 9, 0, \"div\", 9);\n            i0.ɵɵtemplate(12, PlanningListComponent_div_12_Template, 11, 0, \"div\", 10);\n            i0.ɵɵtemplate(13, PlanningListComponent_div_13_Template, 2, 3, \"div\", 11);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"@fadeInDown\", undefined);\n            i0.ɵɵadvance(10);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.plannings.length === 0);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.plannings.length > 0);\n          }\n        },\n        dependencies: [i5.NgClass, i5.NgForOf, i5.NgIf, i3.RouterLink, i5.DatePipe, i6.HighlightPresencePipe],\n        styles: [\"@keyframes _ngcontent-%COMP%_pulse{0%{box-shadow:0 0 #7c3aed66}70%{box-shadow:0 0 0 10px #7c3aed00}to{box-shadow:0 0 #7c3aed00}}.card-hover[_ngcontent-%COMP%]{transition:all .3s ease}.card-hover[_ngcontent-%COMP%]:hover{transform:translateY(-5px);box-shadow:0 10px 20px #0000001a}.fade-in[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeIn .5s ease-in-out}@keyframes _ngcontent-%COMP%_fadeIn{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}.stagger-item[_ngcontent-%COMP%]{opacity:0;transform:translateY(20px)}.planning-header[_ngcontent-%COMP%]{position:relative;display:inline-block}.underline-animation[_ngcontent-%COMP%]{position:absolute;bottom:-8px;left:0;width:100%;height:3px;background:linear-gradient(90deg,#7c3aed,#3b82f6);border-radius:3px;transition:all .4s cubic-bezier(.68,-.55,.265,1.55)}.planning-header[_ngcontent-%COMP%]:hover   .underline-animation[_ngcontent-%COMP%]{transform:scaleX(1.05) translateY(-1px);box-shadow:0 2px 8px #7c3aed80}.add-button[_ngcontent-%COMP%]{transition:all .3s ease;position:relative;overflow:hidden}.add-button[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,.2),transparent);transition:all .5s ease}.add-button[_ngcontent-%COMP%]:hover:before{left:100%}.grid[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{transition:all .3s cubic-bezier(.25,.8,.25,1);backface-visibility:hidden;perspective:1000px}.grid[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;inset:-1px;background:linear-gradient(45deg,#7c3aed,#4f46e5,#3b82f6,#7c3aed);z-index:-1;border-radius:.5rem;opacity:0;transition:opacity .4s ease}.grid[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:hover:before{opacity:.08}.grid[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:hover   h3[_ngcontent-%COMP%], .grid[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:hover   a.hover\\\\:text-purple-600[_ngcontent-%COMP%]{color:#4a5568!important;font-weight:600}@keyframes _ngcontent-%COMP%_attention-pulse{0%{box-shadow:0 0 #7c3aed66}70%{box-shadow:0 0 0 8px #7c3aed00}to{box-shadow:0 0 #7c3aed00}}.grid[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(1){background:rgba(255,255,255,.95)!important;border:2px solid rgba(124,58,237,.1)}.grid[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(1)   h3[_ngcontent-%COMP%], .grid[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(1)   p[_ngcontent-%COMP%], .grid[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(1)   span[_ngcontent-%COMP%], .grid[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(1) a{color:#4a5568!important;font-weight:600}.grid[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{position:relative;overflow:hidden}.grid[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:after{content:\\\"\\\";display:block;position:absolute;width:100%;height:100%;top:0;left:0;pointer-events:none;background-image:radial-gradient(circle,#fff 10%,transparent 10.01%);background-repeat:no-repeat;background-position:50%;transform:scale(10);opacity:0;transition:transform .5s,opacity 1s}.grid[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:active:after{transform:scale(0);opacity:.3;transition:0s}.grid[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:hover{transform:rotateX(.5deg) rotateY(.5deg) translateY(-2px)}.planning-title[_ngcontent-%COMP%]{display:block;color:#2d3748;font-weight:600;text-shadow:0 1px 2px rgba(0,0,0,.05);transition:all .3s ease;padding:2px 0}.planning-title[_ngcontent-%COMP%]:hover{color:#6b46c1!important;text-decoration:none}.details-link[_ngcontent-%COMP%]{position:relative;transition:all .3s ease;padding-right:5px;color:#6b46c1!important;font-weight:600}.details-link[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;bottom:-2px;left:0;width:0;height:2px;background-color:#6b46c1;transition:width .3s ease}.details-link[_ngcontent-%COMP%]:hover:after{width:100%}.reunion-count[_ngcontent-%COMP%]{display:flex;align-items:center}.reunion-count[_ngcontent-%COMP%]:before{content:\\\"\\\";display:inline-block;width:8px;height:8px;border-radius:50%;background-color:#4a5568;margin-right:6px}\"],\n        data: {\n          animation: [\n          // Animation pour l'entrée des cartes de planning (plus fluide)\n          trigger('staggerAnimation', [transition('* => *', [query(':enter', [style({\n            opacity: 0,\n            transform: 'translateY(20px) scale(0.95)'\n          }), stagger('100ms', [animate('0.6s cubic-bezier(0.25, 0.8, 0.25, 1)', keyframes([style({\n            opacity: 0,\n            transform: 'translateY(20px) scale(0.95)',\n            offset: 0\n          }), style({\n            opacity: 0.6,\n            transform: 'translateY(10px) scale(0.98)',\n            offset: 0.4\n          }), style({\n            opacity: 1,\n            transform: 'translateY(0) scale(1)',\n            offset: 1.0\n          })]))])], {\n            optional: true\n          })])]),\n          // Animation pour le survol des cartes (plus douce)\n          trigger('cardHover', [state('default', style({\n            transform: 'scale(1) translateY(0)',\n            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'\n          })), state('hovered', style({\n            transform: 'scale(1.02) translateY(-3px)',\n            boxShadow: '0 15px 20px -5px rgba(0, 0, 0, 0.08), 0 8px 8px -5px rgba(0, 0, 0, 0.03)'\n          })), transition('default => hovered', [animate('0.4s cubic-bezier(0.25, 0.8, 0.25, 1)')]), transition('hovered => default', [animate('0.3s cubic-bezier(0.25, 0.8, 0.25, 1)')])]),\n          // Animation pour l'en-tête\n          trigger('fadeInDown', [transition(':enter', [style({\n            opacity: 0,\n            transform: 'translateY(-20px)'\n          }), animate('0.5s ease-out', style({\n            opacity: 1,\n            transform: 'translateY(0)'\n          }))])])]\n        }\n      });\n    }\n  }\n  return PlanningListComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}