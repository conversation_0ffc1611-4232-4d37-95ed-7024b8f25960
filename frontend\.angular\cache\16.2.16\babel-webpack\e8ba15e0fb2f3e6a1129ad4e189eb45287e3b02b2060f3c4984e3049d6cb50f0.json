{"ast": null, "code": "import { BehaviorSubject, Observable, of, throwError, retry, EMPTY } from 'rxjs';\nimport { map, catchError, tap, filter, debounceTime, distinctUntilChanged, shareReplay } from 'rxjs/operators';\nimport { environment } from '../../environments/environment';\nimport { MessageType, CallType, CallStatus } from '../models/message.model';\nimport { GET_CONVERSATIONS_QUERY, GET_NOTIFICATIONS_QUERY, NOTIFICATION_SUBSCRIPTION, GET_CONVERSATION_QUERY, SEND_MESSAGE_MUTATION, MARK_AS_READ_MUTATION, MESSAGE_SENT_SUBSCRIPTION, USER_STATUS_SUBSCRIPTION, GET_USER_QUERY, GET_ALL_USER_QUERY, CONVERSATION_UPDATED_SUBSCRIPTION, SEARCH_MESSAGES_QUERY, GET_UNREAD_MESSAGES_QUERY, SET_USER_ONLINE_MUTATION, SET_USER_OFFLINE_MUTATION, START_TYPING_MUTATION, STOP_TYPING_MUTATION, TYPING_INDICATOR_SUBSCRIPTION, GET_CURRENT_USER_QUERY, REACT_TO_MESSAGE_MUTATION, FORWARD_MESSAGE_MUTATION, PIN_MESSAGE_MUTATION, CREATE_GROUP_MUTATION, UPDATE_GROUP_MUTATION, DELETE_GROUP_MUTATION, LEAVE_GROUP_MUTATION, GET_GROUP_QUERY, GET_USER_GROUPS_QUERY, EDIT_MESSAGE_MUTATION, DELETE_MESSAGE_MUTATION, GET_MESSAGES_QUERY, GET_NOTIFICATIONS_ATTACHAMENTS, MARK_NOTIFICATION_READ_MUTATION, NOTIFICATIONS_READ_SUBSCRIPTION, CREATE_CONVERSATION_MUTATION, DELETE_NOTIFICATION_MUTATION, DELETE_MULTIPLE_NOTIFICATIONS_MUTATION, DELETE_ALL_NOTIFICATIONS_MUTATION,\n// Requêtes et mutations pour les appels\nCALL_HISTORY_QUERY, CALL_DETAILS_QUERY, CALL_STATS_QUERY, INITIATE_CALL_MUTATION, SEND_CALL_SIGNAL_MUTATION, CALL_SIGNAL_SUBSCRIPTION, INCOMING_CALL_SUBSCRIPTION, GET_VOICE_MESSAGES_QUERY } from '../graphql/message.graphql';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"apollo-angular\";\nimport * as i2 from \"./logger.service\";\nexport let MessageService = /*#__PURE__*/(() => {\n  class MessageService {\n    constructor(apollo, logger, zone) {\n      this.apollo = apollo;\n      this.logger = logger;\n      this.zone = zone;\n      // État partagé\n      this.activeConversation = new BehaviorSubject(null);\n      this.notifications = new BehaviorSubject([]);\n      this.notificationCache = new Map();\n      this.notificationCount = new BehaviorSubject(0);\n      this.onlineUsers = new Map();\n      this.subscriptions = [];\n      this.CACHE_DURATION = 300000;\n      this.lastFetchTime = 0;\n      // Propriétés pour les appels\n      this.activeCall = new BehaviorSubject(null);\n      this.incomingCall = new BehaviorSubject(null);\n      this.callSignals = new BehaviorSubject(null);\n      this.localStream = null;\n      this.remoteStream = null;\n      this.peerConnection = null;\n      // Observables publics pour les appels\n      this.activeCall$ = this.activeCall.asObservable();\n      this.incomingCall$ = this.incomingCall.asObservable();\n      this.callSignals$ = this.callSignals.asObservable();\n      this.localStream$ = new BehaviorSubject(null);\n      this.remoteStream$ = new BehaviorSubject(null);\n      // Configuration WebRTC\n      this.rtcConfig = {\n        iceServers: [{\n          urls: 'stun:stun.l.google.com:19302'\n        }, {\n          urls: 'stun:stun1.l.google.com:19302'\n        }]\n      };\n      this.usersCache = [];\n      // Pagination metadata for user list\n      this.currentUserPagination = {\n        totalCount: 0,\n        totalPages: 0,\n        currentPage: 1,\n        hasNextPage: false,\n        hasPreviousPage: false\n      };\n      // Observables publics\n      this.activeConversation$ = this.activeConversation.asObservable();\n      this.notifications$ = this.notifications.asObservable();\n      this.notificationCount$ = this.notificationCount.asObservable();\n      // Propriétés pour la gestion des sons\n      this.sounds = {};\n      this.isPlaying = {};\n      this.muted = false;\n      // --------------------------------------------------------------------------\n      // Section 2: Méthodes pour les Notifications\n      // --------------------------------------------------------------------------\n      // Propriétés pour la pagination des notifications\n      this.notificationPagination = {\n        currentPage: 1,\n        limit: 10,\n        hasMoreNotifications: true\n      };\n      // --------------------------------------------------------------------------\n      // Section 4: Subscriptions et Gestion Temps Réel\n      // --------------------------------------------------------------------------\n      // ✅ Optimized subscription with connection pooling and caching\n      this.subscriptionCache = new Map();\n      this.subscriptionRefCount = new Map();\n      this.toSafeISOString = date => {\n        if (!date) return undefined;\n        return typeof date === 'string' ? date : date.toISOString();\n      };\n      this.loadNotificationsFromLocalStorage();\n      this.initSubscriptions();\n      this.startCleanupInterval();\n      this.preloadSounds();\n    }\n    /**\n     * Charge les notifications depuis le localStorage\n     * @private\n     */\n    loadNotificationsFromLocalStorage() {\n      try {\n        const savedNotifications = localStorage.getItem('notifications');\n        if (savedNotifications) {\n          const notifications = JSON.parse(savedNotifications);\n          this.notificationCache.clear();\n          notifications.forEach(notification => {\n            if (notification && notification.id) {\n              this.notificationCache.set(notification.id, notification);\n            }\n          });\n          this.notifications.next(Array.from(this.notificationCache.values()));\n          this.updateUnreadCount();\n        }\n      } catch (error) {\n        // Handle error silently\n      }\n    }\n    initSubscriptions() {\n      this.zone.runOutsideAngular(() => {\n        this.subscribeToNewNotifications().subscribe();\n        this.subscribeToNotificationsRead().subscribe();\n        this.subscribeToIncomingCalls().subscribe();\n        // 🔥 AJOUT: Subscription générale pour l'utilisateur\n      });\n\n      this.subscribeToUserStatus();\n    }\n    /**\n     * S'abonne aux appels entrants\n     */\n    subscribeToIncomingCalls() {\n      return this.apollo.subscribe({\n        query: INCOMING_CALL_SUBSCRIPTION\n      }).pipe(map(({\n        data\n      }) => {\n        if (!data?.incomingCall) {\n          return null;\n        }\n        // Gérer l'appel entrant\n        this.handleIncomingCall(data.incomingCall);\n        return data.incomingCall;\n      }), catchError(error => {\n        this.logger.error('Error in incoming call subscription', error);\n        return of(null);\n      }));\n    }\n    /**\n     * Gère un appel entrant\n     */\n    handleIncomingCall(call) {\n      this.incomingCall.next(call);\n      this.play('ringtone', true);\n    }\n    // --------------------------------------------------------------------------\n    // Section: Subscriptions aux événements temps réel\n    // --------------------------------------------------------------------------\n    /**\n     * S'abonne aux nouveaux messages\n     */\n    subscribeToMessages() {\n      return this.apollo.subscribe({\n        query: MESSAGE_SENT_SUBSCRIPTION\n      }).pipe(map(({\n        data\n      }) => {\n        if (!data?.messageSent) {\n          return null;\n        }\n        return this.normalizeMessage(data.messageSent);\n      }), catchError(error => {\n        this.logger.error('Error in message subscription', error);\n        return of(null);\n      }));\n    }\n    /**\n     * S'abonne aux indicateurs de frappe\n     */\n    subscribeToTypingIndicators() {\n      return this.apollo.subscribe({\n        query: TYPING_INDICATOR_SUBSCRIPTION\n      }).pipe(map(({\n        data\n      }) => {\n        if (!data?.typingIndicator) {\n          return null;\n        }\n        return data.typingIndicator;\n      }), catchError(error => {\n        this.logger.error('Error in typing indicator subscription', error);\n        return of(null);\n      }));\n    }\n    // Méthodes de frappe déplacées vers la fin du fichier pour éviter les doublons\n    /**\n     * Initie un appel\n     */\n    initiateCall(recipientId, callType) {\n      return this.apollo.mutate({\n        mutation: INITIATE_CALL_MUTATION,\n        variables: {\n          recipientId,\n          callType\n        }\n      }).pipe(map(result => {\n        if (!result.data?.initiateCall) {\n          throw new Error('Failed to initiate call');\n        }\n        return result.data.initiateCall;\n      }), catchError(error => {\n        this.logger.error('Error initiating call', error);\n        return throwError(() => new Error('Failed to initiate call'));\n      }));\n    }\n    /**\n     * Envoie un message avec fichier\n     */\n    sendMessageWithFile(senderId, receiverId, content, file) {\n      return this.apollo.mutate({\n        mutation: SEND_MESSAGE_MUTATION,\n        variables: {\n          senderId,\n          receiverId,\n          content,\n          file\n        }\n      }).pipe(map(result => {\n        if (!result.data?.sendMessage) {\n          throw new Error('Failed to send message');\n        }\n        return this.normalizeMessage(result.data.sendMessage);\n      }), catchError(error => {\n        this.logger.error('Error sending message with file', error);\n        return throwError(() => new Error('Failed to send message'));\n      }));\n    }\n    // Méthode sendMessage déplacée vers la fin du fichier pour éviter les doublons\n    /**\n     * S'abonne aux notifications\n     */\n    subscribeToNotifications() {\n      return this.apollo.subscribe({\n        query: NOTIFICATION_SUBSCRIPTION\n      }).pipe(map(({\n        data\n      }) => {\n        if (!data?.notificationReceived) {\n          return null;\n        }\n        return data.notificationReceived;\n      }), catchError(error => {\n        this.logger.error('Error in notification subscription', error);\n        return of(null);\n      }));\n    }\n    /**\n     * Marque une notification comme lue\n     */\n    markNotificationAsRead(notificationId) {\n      return this.apollo.mutate({\n        mutation: MARK_NOTIFICATION_READ_MUTATION,\n        variables: {\n          notificationId\n        }\n      }).pipe(map(result => result.data?.markNotificationAsRead || false), catchError(error => {\n        this.logger.error('Error marking notification as read', error);\n        return of(false);\n      }));\n    }\n    /**\n     * Crée ou récupère une conversation avec un utilisateur\n     */\n    createOrGetConversation(userId) {\n      return this.apollo.mutate({\n        mutation: CREATE_CONVERSATION_MUTATION,\n        variables: {\n          userId\n        }\n      }).pipe(map(result => {\n        if (!result.data?.createConversation) {\n          throw new Error('Failed to create or get conversation');\n        }\n        return this.normalizeConversation(result.data.createConversation);\n      }), catchError(error => {\n        this.logger.error('Error creating or getting conversation', error);\n        return throwError(() => new Error('Failed to create or get conversation'));\n      }));\n    }\n    // --------------------------------------------------------------------------\n    // Section: Gestion des sons (intégré depuis SoundService)\n    // --------------------------------------------------------------------------\n    /**\n     * Précharge les sons utilisés dans l'application\n     */\n    preloadSounds() {\n      this.loadSound('ringtone', 'assets/sounds/ringtone.mp3');\n      this.loadSound('call-end', 'assets/sounds/call-end.mp3');\n      this.loadSound('call-connected', 'assets/sounds/call-connected.mp3');\n      this.loadSound('notification', 'assets/sounds/notification.mp3');\n    }\n    /**\n     * Charge un fichier audio\n     * @param name Nom du son\n     * @param path Chemin du fichier\n     */\n    loadSound(name, path) {\n      try {\n        const audio = new Audio(path);\n        audio.load();\n        this.sounds[name] = audio;\n        this.isPlaying[name] = false;\n        audio.addEventListener('ended', () => {\n          this.isPlaying[name] = false;\n        });\n      } catch (error) {\n        // Handle error silently\n      }\n    }\n    /**\n     * Joue un son\n     * @param name Nom du son\n     * @param loop Lecture en boucle\n     */\n    play(name, loop = false) {\n      if (this.muted) {\n        return;\n      }\n      try {\n        const sound = this.sounds[name];\n        if (!sound) {\n          return;\n        }\n        sound.loop = loop;\n        if (!this.isPlaying[name]) {\n          sound.currentTime = 0;\n          sound.play().catch(error => {\n            // Handle error silently\n          });\n          this.isPlaying[name] = true;\n        }\n      } catch (error) {\n        // Handle error silently\n      }\n    }\n    /**\n     * Arrête un son\n     * @param name Nom du son\n     */\n    stop(name) {\n      try {\n        const sound = this.sounds[name];\n        if (!sound) {\n          return;\n        }\n        if (this.isPlaying[name]) {\n          sound.pause();\n          sound.currentTime = 0;\n          this.isPlaying[name] = false;\n        }\n      } catch (error) {\n        // Handle error silently\n      }\n    }\n    /**\n     * Arrête tous les sons\n     */\n    stopAllSounds() {\n      Object.keys(this.sounds).forEach(name => {\n        this.stop(name);\n      });\n    }\n    /**\n     * Active ou désactive le son\n     * @param muted True pour désactiver le son, false pour l'activer\n     */\n    setMuted(muted) {\n      this.muted = muted;\n      if (muted) {\n        this.stopAllSounds();\n      }\n    }\n    /**\n     * Vérifie si le son est désactivé\n     * @returns True si le son est désactivé, false sinon\n     */\n    isMuted() {\n      return this.muted;\n    }\n    /**\n     * Joue le son de notification\n     */\n    playNotificationSound() {\n      console.log('MessageService: Tentative de lecture du son de notification');\n      if (this.muted) {\n        console.log('MessageService: Son désactivé, notification ignorée');\n        return;\n      }\n      // Créer une mélodie agréable avec l'API Web Audio\n      try {\n        // Créer un contexte audio\n        const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n        // 🎵 TESTEZ DIFFÉRENTS SONS - Décommentez celui que vous voulez tester !\n        // SON 1: Mélodie douce (WhatsApp style) - ACTUEL\n        this.playNotificationMelody1(audioContext);\n        // SON 2: Mélodie montante (iPhone style) - Décommentez pour tester\n        // this.playNotificationMelody2(audioContext);\n        // SON 3: Mélodie descendante (Messenger style) - Décommentez pour tester\n        // this.playNotificationMelody3(audioContext);\n        // SON 4: Triple note (Discord style) - Décommentez pour tester\n        // this.playNotificationMelody4(audioContext);\n        // SON 5: Cloche douce (Slack style) - Décommentez pour tester\n        // this.playNotificationMelody5(audioContext);\n        console.log('MessageService: Son de notification mélodieux généré avec succès');\n      } catch (error) {\n        console.error('MessageService: Erreur lors de la génération du son:', error);\n        // Fallback à la méthode originale en cas d'erreur\n        try {\n          const audio = new Audio('assets/sounds/notification.mp3');\n          audio.volume = 0.7; // Volume plus doux\n          audio.play().catch(err => {\n            console.error('MessageService: Erreur lors de la lecture du fichier son:', err);\n          });\n        } catch (audioError) {\n          console.error('MessageService: Exception lors de la lecture du fichier son:', audioError);\n        }\n      }\n    }\n    // 🎵 SON 1: Mélodie douce (WhatsApp style)\n    playNotificationMelody1(audioContext) {\n      this.playNotificationTone(audioContext, 0, 659.25, 0.15); // E5\n      this.playNotificationTone(audioContext, 0.15, 523.25, 0.15); // C5\n    }\n    // 🎵 SON 2: Mélodie montante (iPhone style)\n    playNotificationMelody2(audioContext) {\n      this.playNotificationTone(audioContext, 0, 523.25, 0.12); // C5\n      this.playNotificationTone(audioContext, 0.12, 659.25, 0.12); // E5\n      this.playNotificationTone(audioContext, 0.24, 783.99, 0.16); // G5\n    }\n    // 🎵 SON 3: Mélodie descendante (Messenger style)\n    playNotificationMelody3(audioContext) {\n      this.playNotificationTone(audioContext, 0, 880, 0.1); // A5\n      this.playNotificationTone(audioContext, 0.1, 659.25, 0.1); // E5\n      this.playNotificationTone(audioContext, 0.2, 523.25, 0.15); // C5\n    }\n    // 🎵 SON 4: Triple note (Discord style)\n    playNotificationMelody4(audioContext) {\n      this.playNotificationTone(audioContext, 0, 698.46, 0.08); // F5\n      this.playNotificationTone(audioContext, 0.08, 698.46, 0.08); // F5\n      this.playNotificationTone(audioContext, 0.16, 880, 0.12); // A5\n    }\n    // 🎵 SON 5: Cloche douce (Slack style)\n    playNotificationMelody5(audioContext) {\n      this.playBellTone(audioContext, 0, 1046.5, 0.4); // C6 - son de cloche\n    }\n    /**\n     * Joue une note individuelle pour la mélodie de notification\n     */\n    playNotificationTone(audioContext, startTime, frequency, duration) {\n      const oscillator = audioContext.createOscillator();\n      const gainNode = audioContext.createGain();\n      // Configurer l'oscillateur pour un son plus doux\n      oscillator.type = 'sine';\n      oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime + startTime);\n      // Configurer le volume avec une enveloppe douce\n      gainNode.gain.setValueAtTime(0, audioContext.currentTime + startTime);\n      gainNode.gain.linearRampToValueAtTime(0.3, audioContext.currentTime + startTime + 0.02);\n      gainNode.gain.linearRampToValueAtTime(0.2, audioContext.currentTime + startTime + duration * 0.7);\n      gainNode.gain.linearRampToValueAtTime(0, audioContext.currentTime + startTime + duration);\n      // Connecter les nœuds\n      oscillator.connect(gainNode);\n      gainNode.connect(audioContext.destination);\n      // Démarrer et arrêter l'oscillateur\n      oscillator.start(audioContext.currentTime + startTime);\n      oscillator.stop(audioContext.currentTime + startTime + duration);\n    }\n    /**\n     * Joue un son de cloche pour les notifications\n     */\n    playBellTone(audioContext, startTime, frequency, duration) {\n      const oscillator = audioContext.createOscillator();\n      const gainNode = audioContext.createGain();\n      // Configurer l'oscillateur pour un son de cloche\n      oscillator.type = 'triangle'; // Son plus doux que sine\n      oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime + startTime);\n      // Enveloppe de cloche (attaque rapide, déclin lent)\n      gainNode.gain.setValueAtTime(0, audioContext.currentTime + startTime);\n      gainNode.gain.linearRampToValueAtTime(0.4, audioContext.currentTime + startTime + 0.01);\n      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + startTime + duration);\n      // Connecter les nœuds\n      oscillator.connect(gainNode);\n      gainNode.connect(audioContext.destination);\n      // Démarrer et arrêter l'oscillateur\n      oscillator.start(audioContext.currentTime + startTime);\n      oscillator.stop(audioContext.currentTime + startTime + duration);\n    }\n    // --------------------------------------------------------------------------\n    // Section 1: Méthodes pour les Messages\n    // --------------------------------------------------------------------------\n    /**\n     * Joue un fichier audio\n     * @param audioUrl URL du fichier audio à jouer\n     * @returns Promise qui se résout lorsque la lecture est terminée\n     */\n    playAudio(audioUrl) {\n      return new Promise((resolve, reject) => {\n        const audio = new Audio(audioUrl);\n        audio.onended = () => {\n          resolve();\n        };\n        audio.onerror = error => {\n          this.logger.error(`[MessageService] Error playing audio:`, error);\n          reject(error);\n        };\n        audio.play().catch(error => {\n          this.logger.error(`[MessageService] Error playing audio:`, error);\n          reject(error);\n        });\n      });\n    }\n    /**\n     * Récupère tous les messages vocaux de l'utilisateur\n     * @returns Observable avec la liste des messages vocaux\n     */\n    getVoiceMessages() {\n      this.logger.debug('[MessageService] Getting voice messages');\n      return this.apollo.watchQuery({\n        query: GET_VOICE_MESSAGES_QUERY,\n        fetchPolicy: 'network-only' // Ne pas utiliser le cache pour cette requête\n      }).valueChanges.pipe(map(result => {\n        const voiceMessages = result.data?.getVoiceMessages || [];\n        this.logger.debug(`[MessageService] Retrieved ${voiceMessages.length} voice messages`);\n        return voiceMessages;\n      }), catchError(error => {\n        this.logger.error('[MessageService] Error fetching voice messages:', error);\n        return throwError(() => new Error('Failed to fetch voice messages'));\n      }));\n    }\n    // Message methods\n    getMessages(senderId, receiverId, conversationId, page = 1, limit = 25 // ✅ Increased batch size for better performance\n    ) {\n      return this.apollo.watchQuery({\n        query: GET_MESSAGES_QUERY,\n        variables: {\n          senderId,\n          receiverId,\n          conversationId,\n          limit,\n          page\n        },\n        fetchPolicy: 'cache-first',\n        errorPolicy: 'all' // ✅ Handle partial errors gracefully\n      }).valueChanges.pipe(map(result => {\n        const messages = result.data?.getMessages || [];\n        // ✅ Batch normalize messages for better performance\n        return this.batchNormalizeMessages(messages);\n      }), catchError(error => {\n        console.error('Error fetching messages:', error);\n        return throwError(() => new Error('Failed to fetch messages'));\n      }));\n    }\n    editMessage(messageId, newContent) {\n      return this.apollo.mutate({\n        mutation: EDIT_MESSAGE_MUTATION,\n        variables: {\n          messageId,\n          newContent\n        }\n      }).pipe(map(result => {\n        if (!result.data?.editMessage) {\n          throw new Error('Failed to edit message');\n        }\n        return this.normalizeMessage(result.data.editMessage);\n      }), catchError(error => {\n        this.logger.error('Error editing message:', error);\n        return throwError(() => new Error('Failed to edit message'));\n      }));\n    }\n    deleteMessage(messageId) {\n      return this.apollo.mutate({\n        mutation: DELETE_MESSAGE_MUTATION,\n        variables: {\n          messageId\n        }\n      }).pipe(map(result => {\n        if (!result.data?.deleteMessage) {\n          throw new Error('Failed to delete message');\n        }\n        return this.normalizeMessage(result.data.deleteMessage);\n      }), catchError(error => {\n        this.logger.error('Error deleting message:', error);\n        return throwError(() => new Error('Failed to delete message'));\n      }));\n    }\n    markMessageAsRead(messageId) {\n      return this.apollo.mutate({\n        mutation: MARK_AS_READ_MUTATION,\n        variables: {\n          messageId\n        }\n      }).pipe(map(result => {\n        if (!result.data?.markMessageAsRead) throw new Error('Failed to mark message as read');\n        return {\n          ...result.data.markMessageAsRead,\n          readAt: new Date()\n        };\n      }), catchError(error => {\n        console.error('Error marking message as read:', error);\n        return throwError(() => new Error('Failed to mark message as read'));\n      }));\n    }\n    reactToMessage(messageId, emoji) {\n      return this.apollo.mutate({\n        mutation: REACT_TO_MESSAGE_MUTATION,\n        variables: {\n          messageId,\n          emoji\n        }\n      }).pipe(map(result => {\n        if (!result.data?.reactToMessage) throw new Error('Failed to react to message');\n        return result.data.reactToMessage;\n      }), catchError(error => {\n        console.error('Error reacting to message:', error);\n        return throwError(() => new Error('Failed to react to message'));\n      }));\n    }\n    forwardMessage(messageId, conversationIds) {\n      return this.apollo.mutate({\n        mutation: FORWARD_MESSAGE_MUTATION,\n        variables: {\n          messageId,\n          conversationIds\n        }\n      }).pipe(map(result => {\n        if (!result.data?.forwardMessage) throw new Error('Failed to forward message');\n        return result.data.forwardMessage.map(msg => ({\n          ...msg,\n          timestamp: msg.timestamp ? this.normalizeDate(msg.timestamp) : new Date()\n        }));\n      }), catchError(error => {\n        console.error('Error forwarding message:', error);\n        return throwError(() => new Error('Failed to forward message'));\n      }));\n    }\n    pinMessage(messageId, conversationId) {\n      return this.apollo.mutate({\n        mutation: PIN_MESSAGE_MUTATION,\n        variables: {\n          messageId,\n          conversationId\n        }\n      }).pipe(map(result => {\n        if (!result.data?.pinMessage) throw new Error('Failed to pin message');\n        return {\n          ...result.data.pinMessage,\n          pinnedAt: new Date()\n        };\n      }), catchError(error => {\n        console.error('Error pinning message:', error);\n        return throwError(() => new Error('Failed to pin message'));\n      }));\n    }\n    searchMessages(query, conversationId, filters = {}) {\n      return this.apollo.watchQuery({\n        query: SEARCH_MESSAGES_QUERY,\n        variables: {\n          query,\n          conversationId,\n          ...filters,\n          dateFrom: this.toSafeISOString(filters.dateFrom),\n          dateTo: this.toSafeISOString(filters.dateTo)\n        },\n        fetchPolicy: 'cache-first',\n        errorPolicy: 'all'\n      }).valueChanges.pipe(map(result => result.data?.searchMessages?.map(msg => ({\n        ...msg,\n        timestamp: this.safeDate(msg.timestamp),\n        sender: this.normalizeUser(msg.sender)\n      })) || []), catchError(error => {\n        console.error('Error searching messages:', error);\n        return throwError(() => new Error('Failed to search messages'));\n      }));\n    }\n    // ✅ Batch normalization for better performance\n    batchNormalizeMessages(messages) {\n      if (!messages || messages.length === 0) return [];\n      return messages.map(msg => {\n        try {\n          return this.normalizeMessage(msg);\n        } catch (error) {\n          console.error('Error normalizing message:', error);\n          // Return minimal valid message on error\n          return {\n            id: msg.id || msg._id || `temp-${Date.now()}`,\n            content: msg.content || '',\n            type: msg.type || MessageType.TEXT,\n            timestamp: this.safeDate(msg.timestamp),\n            isRead: false,\n            sender: msg.sender ? this.normalizeUser(msg.sender) : {\n              id: this.getCurrentUserId(),\n              username: 'Unknown'\n            }\n          };\n        }\n      });\n    }\n    getUnreadMessages(userId) {\n      return this.apollo.watchQuery({\n        query: GET_UNREAD_MESSAGES_QUERY,\n        variables: {\n          userId\n        },\n        fetchPolicy: 'network-only'\n      }).valueChanges.pipe(map(result => result.data?.getUnreadMessages?.map(msg => ({\n        ...msg,\n        timestamp: this.safeDate(msg.timestamp),\n        sender: this.normalizeUser(msg.sender)\n      })) || []), catchError(error => {\n        console.error('Error fetching unread messages:', error);\n        return throwError(() => new Error('Failed to fetch unread messages'));\n      }));\n    }\n    setActiveConversation(conversationId) {\n      this.activeConversation.next(conversationId);\n    }\n    getConversations() {\n      return this.apollo.watchQuery({\n        query: GET_CONVERSATIONS_QUERY,\n        fetchPolicy: 'network-only'\n      }).valueChanges.pipe(map(result => {\n        const conversations = result.data?.getConversations || [];\n        return conversations.map(conv => this.normalizeConversation(conv));\n      }), catchError(error => {\n        console.error('Error fetching conversations:', error);\n        return throwError(() => new Error('Failed to load conversations'));\n      }));\n    }\n    getConversation(conversationId, limit, page) {\n      this.logger.info(`[MessageService] Getting conversation: ${conversationId}, limit: ${limit}, page: ${page}`);\n      const variables = {\n        conversationId\n      };\n      // Ajouter les paramètres de pagination s'ils sont fournis\n      if (limit !== undefined) {\n        variables.limit = limit;\n      } else {\n        variables.limit = 10; // Valeur par défaut\n      }\n      // Calculer l'offset à partir de la page si elle est fournie\n      if (page !== undefined) {\n        // La requête GraphQL utilise offset, donc nous devons convertir la page en offset\n        const offset = (page - 1) * variables.limit;\n        variables.offset = offset;\n        this.logger.debug(`[MessageService] Calculated offset: ${offset} from page: ${page} and limit: ${variables.limit}`);\n      } else {\n        variables.offset = 0; // Valeur par défaut\n      }\n\n      this.logger.debug(`[MessageService] Final pagination parameters: limit=${variables.limit}, offset=${variables.offset}`);\n      return this.apollo.watchQuery({\n        query: GET_CONVERSATION_QUERY,\n        variables: variables,\n        fetchPolicy: 'network-only',\n        errorPolicy: 'all'\n      }).valueChanges.pipe(retry(2),\n      // Réessayer 2 fois en cas d'erreur\n      map(result => {\n        this.logger.debug(`[MessageService] Conversation response received:`, result);\n        const conv = result.data?.getConversation;\n        if (!conv) {\n          this.logger.error(`[MessageService] Conversation not found: ${conversationId}`);\n          throw new Error('Conversation not found');\n        }\n        this.logger.debug(`[MessageService] Normalizing conversation: ${conversationId}`);\n        const normalizedConversation = this.normalizeConversation(conv);\n        this.logger.info(`[MessageService] Conversation loaded successfully: ${conversationId}, participants: ${normalizedConversation.participants?.length || 0}, messages: ${normalizedConversation.messages?.length || 0}`);\n        return normalizedConversation;\n      }), catchError(error => {\n        this.logger.error(`[MessageService] Error fetching conversation:`, error);\n        return throwError(() => new Error('Failed to load conversation'));\n      }));\n    }\n    createConversation(userId) {\n      this.logger.info(`[MessageService] Creating conversation with user: ${userId}`);\n      if (!userId) {\n        this.logger.error(`[MessageService] Cannot create conversation: userId is undefined`);\n        return throwError(() => new Error('User ID is required to create a conversation'));\n      }\n      return this.apollo.mutate({\n        mutation: CREATE_CONVERSATION_MUTATION,\n        variables: {\n          userId\n        }\n      }).pipe(map(result => {\n        this.logger.debug(`[MessageService] Conversation creation response:`, result);\n        const conversation = result.data?.createConversation;\n        if (!conversation) {\n          this.logger.error(`[MessageService] Failed to create conversation with user: ${userId}`);\n          throw new Error('Failed to create conversation');\n        }\n        try {\n          const normalizedConversation = this.normalizeConversation(conversation);\n          this.logger.info(`[MessageService] Conversation created successfully: ${normalizedConversation.id}`);\n          return normalizedConversation;\n        } catch (error) {\n          this.logger.error(`[MessageService] Error normalizing created conversation:`, error);\n          throw new Error('Error processing created conversation');\n        }\n      }), catchError(error => {\n        this.logger.error(`[MessageService] Error creating conversation with user ${userId}:`, error);\n        return throwError(() => new Error(`Failed to create conversation: ${error.message}`));\n      }));\n    }\n    /**\n     * Récupère une conversation existante ou en crée une nouvelle si elle n'existe pas\n     * @param userId ID de l'utilisateur avec qui créer/récupérer une conversation\n     * @returns Observable avec la conversation\n     */\n    getOrCreateConversation(userId) {\n      this.logger.info(`[MessageService] Getting or creating conversation with user: ${userId}`);\n      if (!userId) {\n        this.logger.error(`[MessageService] Cannot get/create conversation: userId is undefined`);\n        return throwError(() => new Error('User ID is required to get/create a conversation'));\n      }\n      // D'abord, essayons de trouver une conversation existante entre les deux utilisateurs\n      return this.getConversations().pipe(map(conversations => {\n        // Récupérer l'ID de l'utilisateur actuel\n        const currentUserId = this.getCurrentUserId();\n        // Chercher une conversation directe (non groupe) entre les deux utilisateurs\n        const existingConversation = conversations.find(conv => {\n          if (conv.isGroup) return false;\n          // Vérifier si la conversation contient les deux utilisateurs\n          const participantIds = conv.participants?.map(p => p.id || p._id) || [];\n          return participantIds.includes(userId) && participantIds.includes(currentUserId);\n        });\n        if (existingConversation) {\n          this.logger.info(`[MessageService] Found existing conversation: ${existingConversation.id}`);\n          return existingConversation;\n        }\n        // Si aucune conversation n'est trouvée, en créer une nouvelle\n        throw new Error('No existing conversation found');\n      }), catchError(error => {\n        this.logger.info(`[MessageService] No existing conversation found, creating new one: ${error.message}`);\n        return this.createConversation(userId);\n      }));\n    }\n    getNotifications(refresh = false, page = 1, limit = 10) {\n      this.logger.info('MessageService', `Fetching notifications, refresh: ${refresh}, page: ${page}, limit: ${limit}`);\n      this.logger.debug('MessageService', 'Using query', {\n        query: GET_NOTIFICATIONS_QUERY\n      });\n      // Si refresh est true, réinitialiser la pagination mais ne pas vider le cache\n      // pour conserver les suppressions locales\n      if (refresh) {\n        this.logger.debug('MessageService', 'Resetting pagination due to refresh');\n        this.notificationPagination.currentPage = 1;\n        this.notificationPagination.hasMoreNotifications = true;\n      }\n      // Mettre à jour les paramètres de pagination\n      this.notificationPagination.currentPage = page;\n      this.notificationPagination.limit = limit;\n      // Récupérer les IDs des notifications supprimées du localStorage\n      const deletedNotificationIds = this.getDeletedNotificationIds();\n      this.logger.debug('MessageService', `Found ${deletedNotificationIds.size} deleted notification IDs in localStorage`);\n      return this.apollo.watchQuery({\n        query: GET_NOTIFICATIONS_QUERY,\n        variables: {\n          page: page,\n          limit: limit\n        },\n        fetchPolicy: refresh ? 'network-only' : 'cache-first'\n      }).valueChanges.pipe(map(result => {\n        this.logger.debug('MessageService', 'Notifications response received');\n        if (result.errors) {\n          this.logger.error('MessageService', 'GraphQL errors:', result.errors);\n          throw new Error(result.errors.map(e => e.message).join(', '));\n        }\n        const notifications = result.data?.getUserNotifications || [];\n        this.logger.debug('MessageService', `Received ${notifications.length} notifications from server for page ${page}`);\n        // Vérifier s'il y a plus de notifications à charger\n        this.notificationPagination.hasMoreNotifications = notifications.length >= limit;\n        if (notifications.length === 0) {\n          this.logger.info('MessageService', 'No notifications received from server');\n          this.notificationPagination.hasMoreNotifications = false;\n        }\n        // Filtrer les notifications supprimées\n        const filteredNotifications = notifications.filter(notif => !deletedNotificationIds.has(notif.id));\n        this.logger.debug('MessageService', `Filtered out ${notifications.length - filteredNotifications.length} deleted notifications`);\n        // Afficher les notifications reçues pour le débogage\n        filteredNotifications.forEach((notif, index) => {\n          console.log(`Notification ${index + 1} (page ${page}):`, {\n            id: notif.id || notif._id,\n            type: notif.type,\n            content: notif.content,\n            isRead: notif.isRead\n          });\n        });\n        // Vérifier si les notifications existent déjà dans le cache avant de les ajouter\n        // Mettre à jour le cache avec les nouvelles notifications\n        this.updateCache(filteredNotifications);\n        // Récupérer toutes les notifications du cache et les TRIER\n        const cachedNotifications = Array.from(this.notificationCache.values());\n        // 🚀 TRI OPTIMISÉ: Les notifications les plus récentes en premier\n        const sortedNotifications = this.sortNotificationsByDate(cachedNotifications);\n        console.log(`📊 SORTED: ${sortedNotifications.length} notifications triées (plus récentes en premier)`);\n        // Mettre à jour le BehaviorSubject avec les notifications TRIÉES\n        this.notifications.next(sortedNotifications);\n        // Mettre à jour le compteur de notifications non lues\n        this.updateUnreadCount();\n        // Sauvegarder les notifications dans le localStorage\n        this.saveNotificationsToLocalStorage();\n        return cachedNotifications;\n      }), catchError(error => {\n        this.logger.error('MessageService', 'Error loading notifications:', error);\n        if (error.graphQLErrors) {\n          this.logger.error('MessageService', 'GraphQL errors:', error.graphQLErrors);\n        }\n        if (error.networkError) {\n          this.logger.error('MessageService', 'Network error:', error.networkError);\n        }\n        return throwError(() => new Error('Failed to load notifications'));\n      }));\n    }\n    /**\n     * Récupère les IDs des notifications supprimées du localStorage\n     * @private\n     * @returns Set contenant les IDs des notifications supprimées\n     */\n    getDeletedNotificationIds() {\n      try {\n        const deletedIds = new Set();\n        const savedNotifications = localStorage.getItem('notifications');\n        // Si aucune notification n'est sauvegardée, retourner un ensemble vide\n        if (!savedNotifications) {\n          return deletedIds;\n        }\n        // Récupérer les IDs des notifications sauvegardées\n        const savedNotificationIds = new Set(JSON.parse(savedNotifications).map(n => n.id));\n        // Récupérer les notifications du serveur (si disponibles dans le cache Apollo)\n        const serverNotifications = this.apollo.client.readQuery({\n          query: GET_NOTIFICATIONS_QUERY\n        })?.getUserNotifications || [];\n        // Pour chaque notification du serveur, vérifier si elle est dans les notifications sauvegardées\n        serverNotifications.forEach(notification => {\n          if (!savedNotificationIds.has(notification.id)) {\n            deletedIds.add(notification.id);\n          }\n        });\n        return deletedIds;\n      } catch (error) {\n        this.logger.error('MessageService', 'Erreur lors de la récupération des IDs de notifications supprimées:', error);\n        return new Set();\n      }\n    }\n    // Méthode pour vérifier s'il y a plus de notifications à charger\n    hasMoreNotifications() {\n      return this.notificationPagination.hasMoreNotifications;\n    }\n    // Méthode pour charger la page suivante de notifications\n    loadMoreNotifications() {\n      const nextPage = this.notificationPagination.currentPage + 1;\n      return this.getNotifications(false, nextPage, this.notificationPagination.limit);\n    }\n    getNotificationById(id) {\n      return this.notifications$.pipe(map(notifications => notifications.find(n => n.id === id)), catchError(error => {\n        this.logger.error('Error finding notification:', error);\n        return throwError(() => new Error('Failed to find notification'));\n      }));\n    }\n    getNotificationCount() {\n      return this.notifications.value?.length || 0;\n    }\n    getNotificationAttachments(notificationId) {\n      return this.apollo.query({\n        query: GET_NOTIFICATIONS_ATTACHAMENTS,\n        variables: {\n          id: notificationId\n        },\n        fetchPolicy: 'network-only'\n      }).pipe(map(result => result.data?.getNotificationAttachments || []), catchError(error => {\n        this.logger.error('Error fetching notification attachments:', error);\n        return throwError(() => new Error('Failed to fetch attachments'));\n      }));\n    }\n    getUnreadNotifications() {\n      return this.notifications$.pipe(map(notifications => notifications.filter(n => !n.isRead)));\n    }\n    /**\n     * Supprime une notification\n     * @param notificationId ID de la notification à supprimer\n     * @returns Observable avec le résultat de l'opération\n     */\n    deleteNotification(notificationId) {\n      this.logger.debug('MessageService', `Suppression de la notification ${notificationId}`);\n      if (!notificationId) {\n        this.logger.warn('MessageService', 'ID de notification invalide');\n        return throwError(() => new Error('ID de notification invalide'));\n      }\n      // Supprimer localement d'abord pour une meilleure expérience utilisateur\n      const removedCount = this.removeNotificationsFromCache([notificationId]);\n      // Appeler le backend pour supprimer la notification\n      return this.apollo.mutate({\n        mutation: DELETE_NOTIFICATION_MUTATION,\n        variables: {\n          notificationId\n        }\n      }).pipe(map(result => {\n        const response = result.data?.deleteNotification;\n        if (!response) {\n          throw new Error('Réponse de suppression invalide');\n        }\n        this.logger.debug('MessageService', 'Résultat de la suppression:', response);\n        return response;\n      }), catchError(error => this.handleDeletionError(error, 'la suppression de la notification', {\n        success: true,\n        message: 'Notification supprimée localement (erreur serveur)'\n      })));\n    }\n    /**\n     * Sauvegarde les notifications dans le localStorage\n     * @private\n     */\n    saveNotificationsToLocalStorage() {\n      try {\n        const notifications = Array.from(this.notificationCache.values());\n        localStorage.setItem('notifications', JSON.stringify(notifications));\n        this.logger.debug('MessageService', 'Notifications sauvegardées localement');\n      } catch (error) {\n        this.logger.error('MessageService', 'Erreur lors de la sauvegarde des notifications:', error);\n      }\n    }\n    /**\n     * Supprime toutes les notifications de l'utilisateur\n     * @returns Observable avec le résultat de l'opération\n     */\n    deleteAllNotifications() {\n      this.logger.debug('MessageService', 'Suppression de toutes les notifications');\n      // Supprimer localement d'abord pour une meilleure expérience utilisateur\n      const count = this.notificationCache.size;\n      const allNotificationIds = Array.from(this.notificationCache.keys());\n      this.removeNotificationsFromCache(allNotificationIds);\n      // Appeler le backend pour supprimer toutes les notifications\n      return this.apollo.mutate({\n        mutation: DELETE_ALL_NOTIFICATIONS_MUTATION\n      }).pipe(map(result => {\n        const response = result.data?.deleteAllNotifications;\n        if (!response) {\n          throw new Error('Réponse de suppression invalide');\n        }\n        this.logger.debug('MessageService', 'Résultat de la suppression de toutes les notifications:', response);\n        return response;\n      }), catchError(error => this.handleDeletionError(error, 'la suppression de toutes les notifications', {\n        success: true,\n        count,\n        message: `${count} notifications supprimées localement (erreur serveur)`\n      })));\n    }\n    /**\n     * Supprime plusieurs notifications\n     * @param notificationIds IDs des notifications à supprimer\n     * @returns Observable avec le résultat de l'opération\n     */\n    deleteMultipleNotifications(notificationIds) {\n      this.logger.debug('MessageService', `Suppression de ${notificationIds.length} notifications`);\n      if (!notificationIds || notificationIds.length === 0) {\n        this.logger.warn('MessageService', 'Aucun ID de notification fourni');\n        return throwError(() => new Error('Aucun ID de notification fourni'));\n      }\n      // Supprimer localement d'abord pour une meilleure expérience utilisateur\n      const count = this.removeNotificationsFromCache(notificationIds);\n      // Appeler le backend pour supprimer les notifications\n      return this.apollo.mutate({\n        mutation: DELETE_MULTIPLE_NOTIFICATIONS_MUTATION,\n        variables: {\n          notificationIds\n        }\n      }).pipe(map(result => {\n        const response = result.data?.deleteMultipleNotifications;\n        if (!response) {\n          throw new Error('Réponse de suppression invalide');\n        }\n        this.logger.debug('MessageService', 'Résultat de la suppression multiple:', response);\n        return response;\n      }), catchError(error => this.handleDeletionError(error, 'la suppression multiple de notifications', {\n        success: count > 0,\n        count,\n        message: `${count} notifications supprimées localement (erreur serveur)`\n      })));\n    }\n    groupNotificationsByType() {\n      return this.notifications$.pipe(map(notifications => {\n        const groups = new Map();\n        notifications.forEach(notif => {\n          if (!groups.has(notif.type)) {\n            groups.set(notif.type, []);\n          }\n          groups.get(notif.type)?.push(notif);\n        });\n        return groups;\n      }));\n    }\n    markAsRead(notificationIds) {\n      this.logger.debug('MessageService', `Marking notifications as read: ${notificationIds?.join(', ') || 'none'}`);\n      if (!notificationIds || notificationIds.length === 0) {\n        this.logger.warn('MessageService', 'No notification IDs provided');\n        return of({\n          success: false,\n          readCount: 0,\n          remainingCount: this.notificationCount.value\n        });\n      }\n      // Vérifier que tous les IDs sont valides\n      const validIds = notificationIds.filter(id => id && typeof id === 'string' && id.trim() !== '');\n      if (validIds.length !== notificationIds.length) {\n        this.logger.error('MessageService', 'Some notification IDs are invalid', {\n          provided: notificationIds,\n          valid: validIds\n        });\n        return throwError(() => new Error('Some notification IDs are invalid'));\n      }\n      this.logger.debug('MessageService', 'Sending mutation to mark notifications as read', validIds);\n      // Mettre à jour localement d'abord pour une meilleure expérience utilisateur\n      this.updateNotificationStatus(validIds, true);\n      // Créer une réponse optimiste\n      const optimisticResponse = {\n        markNotificationsAsRead: {\n          success: true,\n          readCount: validIds.length,\n          remainingCount: Math.max(0, this.notificationCount.value - validIds.length)\n        }\n      };\n      // Afficher des informations de débogage supplémentaires\n      console.log('Sending markNotificationsAsRead mutation with variables:', {\n        notificationIds: validIds\n      });\n      console.log('Using mutation:', MARK_NOTIFICATION_READ_MUTATION);\n      return this.apollo.mutate({\n        mutation: MARK_NOTIFICATION_READ_MUTATION,\n        variables: {\n          notificationIds: validIds\n        },\n        optimisticResponse: optimisticResponse,\n        errorPolicy: 'all',\n        fetchPolicy: 'no-cache' // Ne pas utiliser le cache pour cette mutation\n      }).pipe(map(result => {\n        this.logger.debug('MessageService', 'Mutation result', result);\n        console.log('Mutation result:', result);\n        // Si nous avons des erreurs GraphQL, les logger mais continuer\n        if (result.errors) {\n          this.logger.error('MessageService', 'GraphQL errors:', result.errors);\n          console.error('GraphQL errors:', result.errors);\n        }\n        // Utiliser la réponse du serveur ou notre réponse optimiste\n        const response = result.data?.markNotificationsAsRead ?? optimisticResponse.markNotificationsAsRead;\n        return response;\n      }), catchError(error => {\n        this.logger.error('MessageService', 'Error marking notifications as read:', error);\n        console.error('Error in markAsRead:', error);\n        // En cas d'erreur, retourner quand même un succès simulé\n        // puisque nous avons déjà mis à jour l'interface utilisateur\n        return of({\n          success: true,\n          readCount: validIds.length,\n          remainingCount: Math.max(0, this.notificationCount.value - validIds.length)\n        });\n      }));\n    }\n    // --------------------------------------------------------------------------\n    // Section 3: Méthodes pour les Appels (SUPPRIMÉES - VOIR SECTION À LA FIN)\n    // --------------------------------------------------------------------------\n    /**\n     * S'abonne aux signaux d'appel\n     * @param callId ID de l'appel\n     * @returns Observable avec les signaux d'appel\n     */\n    subscribeToCallSignals(callId) {\n      return this.apollo.subscribe({\n        query: CALL_SIGNAL_SUBSCRIPTION,\n        variables: {\n          callId\n        }\n      }).pipe(map(({\n        data\n      }) => {\n        if (!data?.callSignal) {\n          throw new Error('No call signal received');\n        }\n        return data.callSignal;\n      }), tap(signal => {\n        this.callSignals.next(signal);\n        this.handleCallSignal(signal);\n      }), catchError(error => {\n        this.logger.error('Error in call signal subscription', error);\n        return throwError(() => new Error('Call signal subscription failed'));\n      }));\n    }\n    /**\n     * Envoie un signal d'appel\n     * @param callId ID de l'appel\n     * @param signalType Type de signal\n     * @param signalData Données du signal\n     * @returns Observable avec le résultat de l'opération\n     */\n    sendCallSignal(callId, signalType, signalData) {\n      return this.apollo.mutate({\n        mutation: SEND_CALL_SIGNAL_MUTATION,\n        variables: {\n          callId,\n          signalType,\n          signalData\n        }\n      }).pipe(map(result => {\n        const success = result.data?.sendCallSignal;\n        if (!success) {\n          throw new Error('Failed to send call signal');\n        }\n        return success;\n      }), catchError(error => {\n        this.logger.error('Error sending call signal', error);\n        return throwError(() => new Error('Failed to send call signal'));\n      }));\n    }\n    /**\n     * Récupère l'historique des appels avec filtres\n     * @param limit Nombre d'appels à récupérer\n     * @param offset Décalage pour la pagination\n     * @param status Filtres de statut\n     * @param type Filtres de type\n     * @param startDate Date de début\n     * @param endDate Date de fin\n     * @returns Observable avec l'historique des appels\n     */\n    getCallHistory(limit = 20, offset = 0, status, type, startDate, endDate) {\n      return this.apollo.watchQuery({\n        query: CALL_HISTORY_QUERY,\n        variables: {\n          limit,\n          offset,\n          status,\n          type,\n          startDate,\n          endDate\n        },\n        fetchPolicy: 'network-only'\n      }).valueChanges.pipe(map(result => {\n        const history = result.data?.callHistory || [];\n        this.logger.debug(`Retrieved ${history.length} call history items`);\n        return history;\n      }), catchError(error => {\n        this.logger.error('Error fetching call history:', error);\n        return throwError(() => new Error('Failed to fetch call history'));\n      }));\n    }\n    /**\n     * Récupère les détails d'un appel spécifique\n     * @param callId ID de l'appel\n     * @returns Observable avec les détails de l'appel\n     */\n    getCallDetails(callId) {\n      return this.apollo.watchQuery({\n        query: CALL_DETAILS_QUERY,\n        variables: {\n          callId\n        },\n        fetchPolicy: 'network-only'\n      }).valueChanges.pipe(map(result => {\n        const details = result.data?.callDetails;\n        if (!details) {\n          throw new Error('Call details not found');\n        }\n        this.logger.debug(`Retrieved call details for: ${callId}`);\n        return details;\n      }), catchError(error => {\n        this.logger.error('Error fetching call details:', error);\n        return throwError(() => new Error('Failed to fetch call details'));\n      }));\n    }\n    /**\n     * Récupère les statistiques d'appels\n     * @returns Observable avec les statistiques d'appels\n     */\n    getCallStats() {\n      return this.apollo.watchQuery({\n        query: CALL_STATS_QUERY,\n        fetchPolicy: 'network-only'\n      }).valueChanges.pipe(map(result => {\n        const stats = result.data?.callStats;\n        if (!stats) {\n          throw new Error('Call stats not found');\n        }\n        this.logger.debug('Retrieved call stats:', stats);\n        return stats;\n      }), catchError(error => {\n        this.logger.error('Error fetching call stats:', error);\n        return throwError(() => new Error('Failed to fetch call stats'));\n      }));\n    }\n    /**\n     * Gère un signal d'appel reçu\n     * @param signal Signal d'appel\n     */\n    handleCallSignal(signal) {\n      switch (signal.type) {\n        case 'ice-candidate':\n          this.handleIceCandidate(signal);\n          break;\n        case 'answer':\n          this.handleAnswer(signal);\n          break;\n        case 'end-call':\n          this.handleEndCall(signal);\n          break;\n        case 'reject':\n          this.handleRejectCall(signal);\n          break;\n        default:\n          this.logger.debug(`Unhandled signal type: ${signal.type}`, signal);\n      }\n    }\n    /**\n     * Gère un candidat ICE reçu\n     * @param signal Signal d'appel contenant un candidat ICE\n     */\n    handleIceCandidate(signal) {\n      if (!this.peerConnection) {\n        this.logger.error('No peer connection available for ICE candidate');\n        return;\n      }\n      try {\n        const candidate = JSON.parse(signal.data);\n        this.peerConnection.addIceCandidate(new RTCIceCandidate(candidate)).catch(error => {\n          this.logger.error('Error adding ICE candidate', error);\n        });\n      } catch (error) {\n        this.logger.error('Error parsing ICE candidate', error);\n      }\n    }\n    /**\n     * Gère une réponse SDP reçue\n     * @param signal Signal d'appel contenant une réponse SDP\n     */\n    handleAnswer(signal) {\n      if (!this.peerConnection) {\n        this.logger.error('No peer connection available for answer');\n        return;\n      }\n      try {\n        const answer = JSON.parse(signal.data);\n        this.peerConnection.setRemoteDescription(new RTCSessionDescription(answer)).catch(error => {\n          this.logger.error('Error setting remote description', error);\n        });\n      } catch (error) {\n        this.logger.error('Error parsing answer', error);\n      }\n    }\n    /**\n     * Gère la fin d'un appel\n     * @param signal Signal d'appel indiquant la fin de l'appel\n     */\n    handleEndCall(signal) {\n      this.stop('ringtone');\n      this.cleanupCall();\n      // Mettre à jour l'état de l'appel actif\n      const currentCall = this.activeCall.value;\n      if (currentCall && currentCall.id === signal.callId) {\n        this.activeCall.next({\n          ...currentCall,\n          status: CallStatus.ENDED,\n          endTime: new Date().toISOString()\n        });\n      }\n    }\n    /**\n     * Gère le rejet d'un appel\n     * @param signal Signal d'appel indiquant le rejet de l'appel\n     */\n    handleRejectCall(signal) {\n      this.stop('ringtone');\n      this.cleanupCall();\n      // Mettre à jour l'état de l'appel actif\n      const currentCall = this.activeCall.value;\n      if (currentCall && currentCall.id === signal.callId) {\n        this.activeCall.next({\n          ...currentCall,\n          status: CallStatus.REJECTED,\n          endTime: new Date().toISOString()\n        });\n      }\n    }\n    /**\n     * Nettoie les ressources d'appel\n     */\n    cleanupCall() {\n      if (this.localStream) {\n        this.localStream.getTracks().forEach(track => track.stop());\n        this.localStream = null;\n        this.localStream$.next(null);\n      }\n      if (this.peerConnection) {\n        this.peerConnection.close();\n        this.peerConnection = null;\n      }\n      this.remoteStream = null;\n      this.remoteStream$.next(null);\n    }\n    /**\n     * Configure les périphériques média pour un appel\n     * @param callType Type d'appel (audio, vidéo)\n     * @returns Observable avec le flux média\n     */\n    setupMediaDevices(callType) {\n      const constraints = {\n        audio: true,\n        video: callType !== CallType.AUDIO ? {\n          width: {\n            ideal: 1280\n          },\n          height: {\n            ideal: 720\n          }\n        } : false\n      };\n      return new Observable(observer => {\n        navigator.mediaDevices.getUserMedia(constraints).then(stream => {\n          observer.next(stream);\n          observer.complete();\n        }).catch(error => {\n          this.logger.error('Error accessing media devices', error);\n          observer.error(new Error('Failed to access media devices'));\n        });\n      });\n    }\n    /**\n     * Génère un ID d'appel unique\n     * @returns ID d'appel unique\n     */\n    generateCallId() {\n      return Date.now().toString() + Math.random().toString(36).substring(2, 9);\n    }\n    // --------------------------------------------------------------------------\n    // Section 4: Méthodes pour les Utilisateurs/Groupes\n    // --------------------------------------------------------------------------\n    // User methods\n    getAllUsers(forceRefresh = false, search, page = 1, limit = 10, sortBy = 'username', sortOrder = 'asc', isOnline) {\n      this.logger.info('MessageService', `Getting users with params: forceRefresh=${forceRefresh}, search=${search || '(empty)'}, page=${page}, limit=${limit}, sortBy=${sortBy}, sortOrder=${sortOrder}, isOnline=${isOnline}`);\n      const now = Date.now();\n      const cacheValid = !forceRefresh && this.usersCache.length > 0 && now - this.lastFetchTime <= this.CACHE_DURATION && !search && page === 1 && limit >= this.usersCache.length;\n      // Use cache only for first page with no filters\n      if (cacheValid) {\n        this.logger.debug('MessageService', `Using cached users (${this.usersCache.length} users)`);\n        return of([...this.usersCache]);\n      }\n      this.logger.debug('MessageService', `Fetching users from server with pagination, fetchPolicy=${forceRefresh ? 'network-only' : 'cache-first'}`);\n      return this.apollo.watchQuery({\n        query: GET_ALL_USER_QUERY,\n        variables: {\n          search,\n          page,\n          limit,\n          sortBy,\n          sortOrder,\n          isOnline: isOnline !== undefined ? isOnline : null\n        },\n        fetchPolicy: forceRefresh ? 'network-only' : 'cache-first'\n      }).valueChanges.pipe(map(result => {\n        this.logger.debug('MessageService', 'Users response received', result);\n        if (result.errors) {\n          this.logger.error('MessageService', 'GraphQL errors in getAllUsers:', result.errors);\n          throw new Error(result.errors.map(e => e.message).join(', '));\n        }\n        if (!result.data?.getAllUsers) {\n          this.logger.warn('MessageService', 'No users data received from server');\n          return [];\n        }\n        const paginatedResponse = result.data.getAllUsers;\n        // Log pagination metadata\n        this.logger.debug('MessageService', 'Pagination metadata:', {\n          totalCount: paginatedResponse.totalCount,\n          totalPages: paginatedResponse.totalPages,\n          currentPage: paginatedResponse.currentPage,\n          hasNextPage: paginatedResponse.hasNextPage,\n          hasPreviousPage: paginatedResponse.hasPreviousPage\n        });\n        // Normalize users with error handling\n        const users = [];\n        for (const user of paginatedResponse.users) {\n          try {\n            if (user) {\n              users.push(this.normalizeUser(user));\n            }\n          } catch (error) {\n            this.logger.warn('MessageService', `Error normalizing user, skipping:`, error);\n          }\n        }\n        this.logger.info('MessageService', `Received ${users.length} users from server (page ${paginatedResponse.currentPage} of ${paginatedResponse.totalPages})`);\n        // Update cache only for first page with no filters\n        if (!search && page === 1 && !isOnline) {\n          this.usersCache = [...users];\n          this.lastFetchTime = Date.now();\n          this.logger.debug('MessageService', `User cache updated with ${users.length} users`);\n        }\n        // Store pagination metadata in a property for component access\n        this.currentUserPagination = {\n          totalCount: paginatedResponse.totalCount,\n          totalPages: paginatedResponse.totalPages,\n          currentPage: paginatedResponse.currentPage,\n          hasNextPage: paginatedResponse.hasNextPage,\n          hasPreviousPage: paginatedResponse.hasPreviousPage\n        };\n        return users;\n      }), catchError(error => {\n        this.logger.error('MessageService', 'Error fetching users:', error);\n        if (error.graphQLErrors) {\n          this.logger.error('MessageService', 'GraphQL errors:', error.graphQLErrors);\n        }\n        if (error.networkError) {\n          this.logger.error('MessageService', 'Network error:', error.networkError);\n        }\n        // Return cache if available (only for first page)\n        if (this.usersCache.length > 0 && page === 1 && !search && !isOnline) {\n          this.logger.warn('MessageService', `Returning ${this.usersCache.length} cached users due to fetch error`);\n          return of([...this.usersCache]);\n        }\n        return throwError(() => new Error(`Failed to fetch users: ${error.message || 'Unknown error'}`));\n      }));\n    }\n    getOneUser(userId) {\n      return this.apollo.watchQuery({\n        query: GET_USER_QUERY,\n        variables: {\n          id: userId\n        },\n        fetchPolicy: 'network-only'\n      }).valueChanges.pipe(map(result => this.normalizeUser(result.data?.getOneUser)), catchError(error => {\n        this.logger.error('MessageService', 'Error fetching user:', error);\n        return throwError(() => new Error('Failed to fetch user'));\n      }));\n    }\n    getCurrentUser() {\n      return this.apollo.watchQuery({\n        query: GET_CURRENT_USER_QUERY,\n        fetchPolicy: 'network-only'\n      }).valueChanges.pipe(map(result => this.normalizeUser(result.data?.getCurrentUser)), catchError(error => {\n        this.logger.error('MessageService', 'Error fetching current user:', error);\n        return throwError(() => new Error('Failed to fetch current user'));\n      }));\n    }\n    setUserOnline(userId) {\n      return this.apollo.mutate({\n        mutation: SET_USER_ONLINE_MUTATION,\n        variables: {\n          userId\n        }\n      }).pipe(map(result => {\n        if (!result.data?.setUserOnline) throw new Error('Failed to set user online');\n        return this.normalizeUser(result.data.setUserOnline);\n      }), catchError(error => {\n        this.logger.error('MessageService', 'Error setting user online:', error);\n        return throwError(() => new Error('Failed to set user online'));\n      }));\n    }\n    setUserOffline(userId) {\n      return this.apollo.mutate({\n        mutation: SET_USER_OFFLINE_MUTATION,\n        variables: {\n          userId\n        }\n      }).pipe(map(result => {\n        if (!result.data?.setUserOffline) throw new Error('Failed to set user offline');\n        return this.normalizeUser(result.data.setUserOffline);\n      }), catchError(error => {\n        this.logger.error('MessageService', 'Error setting user offline:', error);\n        return throwError(() => new Error('Failed to set user offline'));\n      }));\n    }\n    // --------------------------------------------------------------------------\n    // Section: Gestion des Groupes\n    // --------------------------------------------------------------------------\n    /**\n     * Crée un nouveau groupe\n     */\n    createGroup(name, participantIds, photo, description) {\n      this.logger.debug('MessageService', `Creating group: ${name} with ${participantIds.length} participants`);\n      if (!name || !participantIds || participantIds.length === 0) {\n        return throwError(() => new Error('Nom du groupe et participants requis'));\n      }\n      return this.apollo.mutate({\n        mutation: CREATE_GROUP_MUTATION,\n        variables: {\n          name,\n          participantIds,\n          photo,\n          description\n        }\n      }).pipe(map(result => {\n        const group = result.data?.createGroup;\n        if (!group) {\n          throw new Error('Échec de la création du groupe');\n        }\n        this.logger.info('MessageService', `Group created successfully: ${group.id}`);\n        return group;\n      }), catchError(error => {\n        this.logger.error('MessageService', 'Error creating group:', error);\n        return throwError(() => new Error('Échec de la création du groupe'));\n      }));\n    }\n    /**\n     * Met à jour un groupe existant\n     */\n    updateGroup(groupId, input) {\n      this.logger.debug('MessageService', `Updating group: ${groupId}`);\n      if (!groupId) {\n        return throwError(() => new Error('ID du groupe requis'));\n      }\n      return this.apollo.mutate({\n        mutation: UPDATE_GROUP_MUTATION,\n        variables: {\n          id: groupId,\n          input\n        }\n      }).pipe(map(result => {\n        const group = result.data?.updateGroup;\n        if (!group) {\n          throw new Error('Échec de la mise à jour du groupe');\n        }\n        this.logger.info('MessageService', `Group updated successfully: ${group.id}`);\n        return group;\n      }), catchError(error => {\n        this.logger.error('MessageService', 'Error updating group:', error);\n        return throwError(() => new Error('Échec de la mise à jour du groupe'));\n      }));\n    }\n    /**\n     * Supprime un groupe\n     */\n    deleteGroup(groupId) {\n      this.logger.debug('MessageService', `Deleting group: ${groupId}`);\n      if (!groupId) {\n        return throwError(() => new Error('ID du groupe requis'));\n      }\n      return this.apollo.mutate({\n        mutation: DELETE_GROUP_MUTATION,\n        variables: {\n          id: groupId\n        }\n      }).pipe(map(result => {\n        const response = result.data?.deleteGroup;\n        if (!response) {\n          throw new Error('Échec de la suppression du groupe');\n        }\n        this.logger.info('MessageService', `Group deleted successfully: ${groupId}`);\n        return response;\n      }), catchError(error => {\n        this.logger.error('MessageService', 'Error deleting group:', error);\n        return throwError(() => new Error('Échec de la suppression du groupe'));\n      }));\n    }\n    /**\n     * Quitte un groupe\n     */\n    leaveGroup(groupId) {\n      this.logger.debug('MessageService', `Leaving group: ${groupId}`);\n      if (!groupId) {\n        return throwError(() => new Error('ID du groupe requis'));\n      }\n      return this.apollo.mutate({\n        mutation: LEAVE_GROUP_MUTATION,\n        variables: {\n          groupId\n        }\n      }).pipe(map(result => {\n        const response = result.data?.leaveGroup;\n        if (!response) {\n          throw new Error('Échec de la sortie du groupe');\n        }\n        this.logger.info('MessageService', `Left group successfully: ${groupId}`);\n        return response;\n      }), catchError(error => {\n        this.logger.error('MessageService', 'Error leaving group:', error);\n        return throwError(() => new Error('Échec de la sortie du groupe'));\n      }));\n    }\n    /**\n     * Récupère les informations d'un groupe\n     */\n    getGroup(groupId) {\n      this.logger.debug('MessageService', `Getting group: ${groupId}`);\n      if (!groupId) {\n        return throwError(() => new Error('ID du groupe requis'));\n      }\n      return this.apollo.query({\n        query: GET_GROUP_QUERY,\n        variables: {\n          id: groupId\n        },\n        fetchPolicy: 'network-only'\n      }).pipe(map(result => {\n        const group = result.data?.getGroup;\n        if (!group) {\n          throw new Error('Groupe non trouvé');\n        }\n        this.logger.info('MessageService', `Group retrieved successfully: ${groupId}`);\n        return group;\n      }), catchError(error => {\n        this.logger.error('MessageService', 'Error getting group:', error);\n        return throwError(() => new Error('Échec de la récupération du groupe'));\n      }));\n    }\n    /**\n     * Récupère les groupes d'un utilisateur\n     */\n    getUserGroups(userId) {\n      this.logger.debug('MessageService', `Getting groups for user: ${userId}`);\n      if (!userId) {\n        return throwError(() => new Error(\"ID de l'utilisateur requis\"));\n      }\n      return this.apollo.query({\n        query: GET_USER_GROUPS_QUERY,\n        variables: {\n          userId\n        },\n        fetchPolicy: 'network-only'\n      }).pipe(map(result => {\n        const groups = result.data?.getUserGroups || [];\n        this.logger.info('MessageService', `Retrieved ${groups.length} groups for user: ${userId}`);\n        return groups;\n      }), catchError(error => {\n        this.logger.error('MessageService', 'Error getting user groups:', error);\n        return throwError(() => new Error('Échec de la récupération des groupes'));\n      }));\n    }\n    subscribeToNewMessages(conversationId) {\n      if (!conversationId) {\n        return throwError(() => new Error('Conversation ID is required'));\n      }\n      // ✅ Use cached subscription if available\n      const cacheKey = `messages_${conversationId}`;\n      if (this.subscriptionCache.has(cacheKey)) {\n        const refCount = this.subscriptionRefCount.get(cacheKey) || 0;\n        this.subscriptionRefCount.set(cacheKey, refCount + 1);\n        return this.subscriptionCache.get(cacheKey);\n      }\n      // ✅ Quick token validation without verbose logging\n      if (!this.isTokenValid()) {\n        return EMPTY;\n      }\n      // ✅ Reduced logging for better performance\n      if (!environment.production) {\n        console.log(`🚀 Setting up subscription: ${conversationId}`);\n      }\n      // ✅ Create optimized subscription with caching and shareReplay\n      const sub$ = this.apollo.subscribe({\n        query: MESSAGE_SENT_SUBSCRIPTION,\n        variables: {\n          conversationId\n        },\n        errorPolicy: 'all' // Handle partial errors gracefully\n      }).pipe(\n      // ✅ Debounce rapid messages to prevent UI flooding\n      debounceTime(10), map(result => {\n        const msg = result.data?.messageSent;\n        if (!msg) {\n          throw new Error('No message payload received');\n        }\n        // ✅ Reduced logging for better performance\n        if (!environment.production) {\n          console.log('⚡ New message via WebSocket:', msg.id);\n        }\n        // Vérifier que l'ID est présent\n        if (!msg.id && !msg._id) {\n          this.logger.warn('⚠️ Message without ID received, generating temp ID');\n          msg.id = `temp-${Date.now()}`;\n        }\n        try {\n          // NORMALISATION RAPIDE du message\n          const normalizedMessage = this.normalizeMessage(msg);\n          this.logger.debug('✅ INSTANT: Message normalized successfully', normalizedMessage);\n          // TRAITEMENT INSTANTANÉ selon le type\n          if (normalizedMessage.type === MessageType.AUDIO || normalizedMessage.type === MessageType.VOICE_MESSAGE || normalizedMessage.attachments && normalizedMessage.attachments.some(att => att.type === 'AUDIO')) {\n            this.logger.debug('🎤 INSTANT: Voice message received in real-time');\n          }\n          // MISE À JOUR IMMÉDIATE de l'UI\n          this.zone.run(() => {\n            this.logger.debug('📡 INSTANT: Updating conversation UI immediately');\n            this.updateConversationWithNewMessage(conversationId, normalizedMessage);\n          });\n          return normalizedMessage;\n        } catch (err) {\n          this.logger.error('❌ Error normalizing message:', err);\n          // Créer un message minimal mais valide pour éviter les erreurs\n          const minimalMessage = {\n            id: msg.id || msg._id || `temp-${Date.now()}`,\n            content: msg.content || '',\n            type: msg.type || MessageType.TEXT,\n            timestamp: this.safeDate(msg.timestamp),\n            isRead: false,\n            sender: msg.sender ? this.normalizeUser(msg.sender) : {\n              id: this.getCurrentUserId(),\n              username: 'Unknown'\n            }\n          };\n          this.logger.debug('🔧 FALLBACK: Created minimal message', minimalMessage);\n          return minimalMessage;\n        }\n      }), catchError(error => {\n        console.error('Message subscription error:', error);\n        return EMPTY;\n      }),\n      // ✅ Filter null values and deduplicate messages\n      filter(message => !!message), distinctUntilChanged((prev, curr) => prev?.id === curr?.id),\n      // ✅ Cache subscription with shareReplay for performance\n      shareReplay({\n        bufferSize: 1,\n        refCount: true\n      }),\n      // ✅ Retry with exponential backoff\n      retry(3));\n      // ✅ Cache the subscription for reuse\n      this.subscriptionCache.set(cacheKey, sub$);\n      this.subscriptionRefCount.set(cacheKey, 1);\n      // ✅ Optimized subscription observer with minimal logging\n      const sub = sub$.subscribe({\n        next: message => {\n          if (!environment.production) {\n            console.log(`✅ Message received:`, message.id);\n          }\n          // ✅ Update conversation immediately\n          this.updateConversationWithNewMessage(conversationId, message);\n        },\n        error: err => {\n          console.error('Subscription error:', err);\n          // ✅ Clean up cache on error\n          this.subscriptionCache.delete(cacheKey);\n          this.subscriptionRefCount.delete(cacheKey);\n        },\n        complete: () => {\n          // ✅ Clean up cache on completion\n          this.subscriptionCache.delete(cacheKey);\n          this.subscriptionRefCount.delete(cacheKey);\n        }\n      });\n      this.subscriptions.push(sub);\n      return sub$;\n    }\n    /**\n     * Met à jour une conversation avec un nouveau message INSTANTANÉMENT\n     * @param conversationId ID de la conversation\n     * @param message Nouveau message\n     */\n    updateConversationWithNewMessage(conversationId, message) {\n      this.logger.debug(`⚡ INSTANT: Updating conversation ${conversationId} with new message ${message.id}`);\n      // MISE À JOUR IMMÉDIATE sans attendre la requête\n      this.zone.run(() => {\n        // Émettre IMMÉDIATEMENT l'événement de conversation active\n        this.activeConversation.next(conversationId);\n        this.logger.debug('📡 INSTANT: Conversation event emitted immediately');\n      });\n      // Mise à jour en arrière-plan (non-bloquante)\n      setTimeout(() => {\n        this.getConversation(conversationId).subscribe({\n          next: conversation => {\n            this.logger.debug(`✅ BACKGROUND: Conversation ${conversationId} refreshed with ${conversation?.messages?.length || 0} messages`);\n          },\n          error: error => {\n            this.logger.error(`⚠️ BACKGROUND: Error refreshing conversation ${conversationId}:`, error);\n          }\n        });\n      }, 0); // Exécution asynchrone immédiate\n    }\n    /**\n     * Rafraîchit les notifications du sender après envoi d'un message\n     */\n    refreshSenderNotifications() {\n      console.log('🔄 SENDER: Refreshing notifications after message sent');\n      // Recharger les notifications en arrière-plan\n      this.getNotifications(true).subscribe({\n        next: notifications => {\n          console.log('🔄 SENDER: Notifications refreshed successfully', notifications.length);\n        },\n        error: error => {\n          console.error('🔄 SENDER: Error refreshing notifications:', error);\n        }\n      });\n    }\n    subscribeToUserStatus() {\n      // Vérifier si l'utilisateur est connecté avec un token valide\n      if (!this.isTokenValid()) {\n        this.logger.warn(\"Tentative d'abonnement au statut utilisateur avec un token invalide ou expiré\");\n        return throwError(() => new Error('Invalid or expired token'));\n      }\n      this.logger.debug(\"Démarrage de l'abonnement au statut utilisateur\");\n      const sub$ = this.apollo.subscribe({\n        query: USER_STATUS_SUBSCRIPTION\n      }).pipe(tap(result => this.logger.debug(\"Données reçues de l'abonnement au statut utilisateur:\", result)), map(result => {\n        const user = result.data?.userStatusChanged;\n        if (!user) {\n          this.logger.error('No status payload received');\n          throw new Error('No status payload received');\n        }\n        return this.normalizeUser(user);\n      }), catchError(error => {\n        this.logger.error('Status subscription error:', error);\n        return throwError(() => new Error('Status subscription failed'));\n      }), retry(3) // Réessayer 3 fois en cas d'erreur\n      );\n\n      const sub = sub$.subscribe();\n      this.subscriptions.push(sub);\n      return sub$;\n    }\n    subscribeToConversationUpdates(conversationId) {\n      const sub$ = this.apollo.subscribe({\n        query: CONVERSATION_UPDATED_SUBSCRIPTION,\n        variables: {\n          conversationId\n        }\n      }).pipe(map(result => {\n        const conv = result.data?.conversationUpdated;\n        if (!conv) throw new Error('No conversation payload received');\n        const normalizedConversation = {\n          ...conv,\n          participants: conv.participants?.map(p => this.normalizeUser(p)) || [],\n          lastMessage: conv.lastMessage ? {\n            ...conv.lastMessage,\n            sender: this.normalizeUser(conv.lastMessage.sender),\n            timestamp: this.safeDate(conv.lastMessage.timestamp),\n            readAt: conv.lastMessage.readAt ? this.safeDate(conv.lastMessage.readAt) : undefined,\n            // Conservez toutes les autres propriétés du message\n            id: conv.lastMessage.id,\n            content: conv.lastMessage.content,\n            type: conv.lastMessage.type,\n            isRead: conv.lastMessage.isRead\n            // ... autres propriétés nécessaires\n          } : null // On conserve null comme dans votre version originale\n        };\n\n        return normalizedConversation; // Assertion de type si nécessaire\n      }), catchError(error => {\n        this.logger.error('MessageService', 'Conversation subscription error:', error);\n        return throwError(() => new Error('Conversation subscription failed'));\n      }));\n      const sub = sub$.subscribe();\n      this.subscriptions.push(sub);\n      return sub$;\n    }\n    subscribeToTypingIndicator(conversationId) {\n      const sub$ = this.apollo.subscribe({\n        query: TYPING_INDICATOR_SUBSCRIPTION,\n        variables: {\n          conversationId\n        }\n      }).pipe(map(result => result.data?.typingIndicator), filter(Boolean), catchError(error => {\n        this.logger.error('MessageService', 'Typing indicator subscription error:', error);\n        return throwError(() => new Error('Typing indicator subscription failed'));\n      }));\n      const sub = sub$.subscribe();\n      this.subscriptions.push(sub);\n      return sub$;\n    }\n    isTokenValid() {\n      const token = localStorage.getItem('token');\n      if (!token) {\n        this.logger.warn('Aucun token trouvé');\n        return false;\n      }\n      try {\n        // Décoder le token JWT (format: header.payload.signature)\n        const parts = token.split('.');\n        if (parts.length !== 3) {\n          this.logger.warn('Format de token invalide');\n          return false;\n        }\n        // Décoder le payload (deuxième partie du token)\n        const payload = JSON.parse(atob(parts[1]));\n        // Vérifier l'expiration\n        if (!payload.exp) {\n          this.logger.warn(\"Token sans date d'expiration\");\n          return false;\n        }\n        const expirationDate = new Date(payload.exp * 1000);\n        const now = new Date();\n        if (expirationDate < now) {\n          this.logger.warn('Token expiré', {\n            expiration: expirationDate.toISOString(),\n            now: now.toISOString()\n          });\n          return false;\n        }\n        return true;\n      } catch (error) {\n        this.logger.error('Erreur lors de la vérification du token:', error);\n        return false;\n      }\n    }\n    subscribeToNotificationsRead() {\n      // Vérifier si l'utilisateur est connecté avec un token valide\n      if (!this.isTokenValid()) {\n        this.logger.warn(\"Tentative d'abonnement aux notifications avec un token invalide ou expiré\");\n        return of([]);\n      }\n      this.logger.debug(\"Démarrage de l'abonnement aux notifications lues\");\n      const sub$ = this.apollo.subscribe({\n        query: NOTIFICATIONS_READ_SUBSCRIPTION\n      }).pipe(tap(result => this.logger.debug(\"Données reçues de l'abonnement aux notifications lues:\", result)), map(result => {\n        const notificationIds = result.data?.notificationsRead || [];\n        this.logger.debug('Notifications marquées comme lues:', notificationIds);\n        this.updateNotificationStatus(notificationIds, true);\n        return notificationIds;\n      }), catchError(err => {\n        this.logger.error('Notifications read subscription error:', err);\n        // Retourner un tableau vide au lieu de propager l'erreur\n        return of([]);\n      }),\n      // Réessayer après un délai en cas d'erreur\n      retry(3) // Réessayer 3 fois en cas d'erreur\n      );\n\n      const sub = sub$.subscribe();\n      this.subscriptions.push(sub);\n      return sub$;\n    }\n    subscribeToNewNotifications() {\n      // Vérifier si l'utilisateur est connecté\n      const token = localStorage.getItem('token');\n      if (!token) {\n        this.logger.warn(\"Tentative d'abonnement aux notifications sans être connecté\");\n        return EMPTY;\n      }\n      this.logger.debug('🚀 INSTANT NOTIFICATION: Setting up real-time subscription');\n      const source$ = this.apollo.subscribe({\n        query: NOTIFICATION_SUBSCRIPTION\n      });\n      const processed$ = source$.pipe(map(result => {\n        const notification = result.data?.notificationReceived;\n        if (!notification) {\n          throw new Error('No notification payload received');\n        }\n        this.logger.debug('⚡ INSTANT: New notification received', notification);\n        const normalized = this.normalizeNotification(notification);\n        // Vérification rapide du cache\n        if (this.notificationCache.has(normalized.id)) {\n          this.logger.debug(`🔄 Notification ${normalized.id} already in cache, skipping`);\n          throw new Error('Notification already exists in cache');\n        }\n        // TRAITEMENT INSTANTANÉ\n        this.logger.debug('📡 INSTANT: Processing notification immediately');\n        // Vérifier si la notification existe déjà pour éviter les doublons\n        const currentNotifications = this.notifications.value;\n        const existingNotification = currentNotifications.find(n => n.id === normalized.id);\n        if (existingNotification) {\n          this.logger.debug('🔄 DUPLICATE: Notification already exists, skipping:', normalized.id);\n          return normalized;\n        }\n        // Son de notification IMMÉDIAT\n        this.playNotificationSound();\n        // Mise à jour INSTANTANÉE du cache\n        this.updateNotificationCache(normalized);\n        // Émettre IMMÉDIATEMENT la notification EN PREMIER\n        this.zone.run(() => {\n          // 🚀 INSERTION EN PREMIER: Nouvelle notification en tête de liste\n          const updatedNotifications = [normalized, ...currentNotifications];\n          this.logger.debug(`⚡ INSTANT: Nouvelle notification ajoutée en PREMIER (${updatedNotifications.length} total)`);\n          this.notifications.next(updatedNotifications);\n          this.notificationCount.next(this.notificationCount.value + 1);\n        });\n        this.logger.debug('✅ INSTANT: Notification processed and emitted', normalized);\n        return normalized;\n      }),\n      // Gestion d'erreurs optimisée\n      catchError(err => {\n        if (err instanceof Error && err.message === 'Notification already exists in cache') {\n          return EMPTY;\n        }\n        this.logger.error('❌ Notification subscription error:', err);\n        return EMPTY;\n      }),\n      // Optimisation: traitement en temps réel\n      tap(notification => {\n        this.logger.debug('⚡ INSTANT: Notification ready for UI update', notification);\n      }));\n      const sub = processed$.subscribe({\n        next: notification => {\n          this.logger.debug('✅ INSTANT: Notification delivered to UI', notification);\n        },\n        error: error => {\n          this.logger.error('❌ CRITICAL: Notification subscription error', error);\n        }\n      });\n      this.subscriptions.push(sub);\n      this.logger.debug('🔗 INSTANT: Notification subscription established');\n      return processed$;\n    }\n    // --------------------------------------------------------------------------\n    // Helpers et Utilitaires\n    // --------------------------------------------------------------------------\n    startCleanupInterval() {\n      this.cleanupInterval = setInterval(() => {\n        this.cleanupExpiredNotifications();\n      }, 3600000);\n    }\n    cleanupExpiredNotifications() {\n      const now = new Date();\n      const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);\n      let expiredCount = 0;\n      this.notificationCache.forEach((notification, id) => {\n        const notificationDate = new Date(notification.timestamp);\n        if (notificationDate < thirtyDaysAgo) {\n          this.notificationCache.delete(id);\n          expiredCount++;\n        }\n      });\n      if (expiredCount > 0) {\n        this.logger.debug(`Cleaned up ${expiredCount} expired notifications`);\n        // 🚀 TRI OPTIMISÉ: Maintenir l'ordre après nettoyage\n        const remainingNotifications = Array.from(this.notificationCache.values());\n        const sortedNotifications = this.sortNotificationsByDate(remainingNotifications);\n        this.notifications.next(sortedNotifications);\n        this.updateUnreadCount();\n      }\n    }\n    /**\n     * Trie les notifications par date (plus récentes en premier)\n     * @param notifications Array de notifications à trier\n     * @returns Array de notifications triées\n     */\n    sortNotificationsByDate(notifications) {\n      return notifications.sort((a, b) => {\n        // Utiliser timestamp ou une date par défaut si manquant\n        const dateA = new Date(a.timestamp || 0);\n        const dateB = new Date(b.timestamp || 0);\n        return dateB.getTime() - dateA.getTime(); // Ordre décroissant (plus récent en premier)\n      });\n    }\n\n    getCurrentUserId() {\n      return localStorage.getItem('userId') || '';\n    }\n    normalizeMessage(message) {\n      if (!message) {\n        this.logger.error('[MessageService] Cannot normalize null or undefined message');\n        throw new Error('Message object is required');\n      }\n      try {\n        // Vérification des champs obligatoires\n        if (!message.id && !message._id) {\n          this.logger.error('[MessageService] Message ID is missing', undefined, message);\n          throw new Error('Message ID is required');\n        }\n        // Normaliser le sender avec gestion d'erreur\n        let normalizedSender;\n        try {\n          normalizedSender = message.sender ? this.normalizeUser(message.sender) : undefined;\n        } catch (error) {\n          this.logger.warn('[MessageService] Error normalizing message sender, using default values', error);\n          normalizedSender = {\n            _id: message.senderId || 'unknown',\n            id: message.senderId || 'unknown',\n            username: 'Unknown User',\n            email: '<EMAIL>',\n            role: 'user',\n            isActive: true\n          };\n        }\n        // Normaliser le receiver si présent\n        let normalizedReceiver;\n        if (message.receiver) {\n          try {\n            normalizedReceiver = this.normalizeUser(message.receiver);\n          } catch (error) {\n            this.logger.warn('[MessageService] Error normalizing message receiver, using default values', error);\n            normalizedReceiver = {\n              _id: message.receiverId || 'unknown',\n              id: message.receiverId || 'unknown',\n              username: 'Unknown User',\n              email: '<EMAIL>',\n              role: 'user',\n              isActive: true\n            };\n          }\n        }\n        // Normaliser les pièces jointes si présentes\n        const normalizedAttachments = message.attachments?.map(att => ({\n          id: att.id || att._id || `attachment-${Date.now()}`,\n          url: att.url || '',\n          type: att.type || 'unknown',\n          name: att.name || 'attachment',\n          size: att.size || 0,\n          duration: att.duration || 0\n        })) || [];\n        // Construire le message normalisé\n        const normalizedMessage = {\n          ...message,\n          _id: message.id || message._id,\n          id: message.id || message._id,\n          content: message.content || '',\n          sender: normalizedSender,\n          timestamp: this.normalizeDate(message.timestamp),\n          readAt: message.readAt ? this.normalizeDate(message.readAt) : undefined,\n          attachments: normalizedAttachments,\n          metadata: message.metadata || null\n        };\n        // Ajouter le receiver seulement s'il existe\n        if (normalizedReceiver) {\n          normalizedMessage.receiver = normalizedReceiver;\n        }\n        this.logger.debug('[MessageService] Message normalized successfully', {\n          messageId: normalizedMessage.id,\n          senderId: normalizedMessage.sender?.id\n        });\n        return normalizedMessage;\n      } catch (error) {\n        this.logger.error('[MessageService] Error normalizing message:', error instanceof Error ? error : new Error(String(error)), message);\n        throw new Error(`Failed to normalize message: ${error instanceof Error ? error.message : String(error)}`);\n      }\n    }\n    normalizeUser(user) {\n      if (!user) {\n        throw new Error('User object is required');\n      }\n      // Vérification des champs obligatoires avec valeurs par défaut\n      const userId = user.id || user._id;\n      if (!userId) {\n        throw new Error('User ID is required');\n      }\n      // Utiliser des valeurs par défaut pour les champs manquants\n      const username = user.username || 'Unknown User';\n      const email = user.email || `user-${userId}@example.com`;\n      const isActive = user.isActive !== undefined && user.isActive !== null ? user.isActive : true;\n      const role = user.role || 'user';\n      // Construire l'objet utilisateur normalisé\n      return {\n        _id: userId,\n        id: userId,\n        username: username,\n        email: email,\n        role: role,\n        isActive: isActive,\n        // Champs optionnels\n        image: user.image ?? null,\n        bio: user.bio,\n        isOnline: user.isOnline || false,\n        lastActive: user.lastActive ? new Date(user.lastActive) : undefined,\n        createdAt: user.createdAt ? new Date(user.createdAt) : undefined,\n        updatedAt: user.updatedAt ? new Date(user.updatedAt) : undefined,\n        followingCount: user.followingCount,\n        followersCount: user.followersCount,\n        postCount: user.postCount\n      };\n    }\n    normalizeConversation(conv) {\n      if (!conv) {\n        this.logger.error('[MessageService] Cannot normalize null or undefined conversation');\n        throw new Error('Conversation object is required');\n      }\n      try {\n        // Vérification des champs obligatoires\n        if (!conv.id && !conv._id) {\n          this.logger.error('[MessageService] Conversation ID is missing', undefined, conv);\n          throw new Error('Conversation ID is required');\n        }\n        // Normaliser les participants avec gestion d'erreur\n        const normalizedParticipants = [];\n        if (conv.participants && Array.isArray(conv.participants)) {\n          for (const participant of conv.participants) {\n            try {\n              if (participant) {\n                normalizedParticipants.push(this.normalizeUser(participant));\n              }\n            } catch (error) {\n              this.logger.warn('[MessageService] Error normalizing participant, skipping', error);\n            }\n          }\n        } else {\n          this.logger.warn('[MessageService] Conversation has no participants or invalid participants array', conv);\n        }\n        // Normaliser les messages avec gestion d'erreur\n        const normalizedMessages = [];\n        if (conv.messages && Array.isArray(conv.messages)) {\n          this.logger.debug('[MessageService] Processing conversation messages', {\n            count: conv.messages.length\n          });\n          for (const message of conv.messages) {\n            try {\n              if (message) {\n                const normalizedMessage = this.normalizeMessage(message);\n                this.logger.debug('[MessageService] Successfully normalized message', {\n                  messageId: normalizedMessage.id,\n                  content: normalizedMessage.content?.substring(0, 20),\n                  sender: normalizedMessage.sender?.username\n                });\n                normalizedMessages.push(normalizedMessage);\n              }\n            } catch (error) {\n              this.logger.warn('[MessageService] Error normalizing message in conversation, skipping', error);\n            }\n          }\n        } else {\n          this.logger.debug('[MessageService] No messages found in conversation or invalid messages array');\n        }\n        // Normaliser le dernier message avec gestion d'erreur\n        let normalizedLastMessage = null;\n        if (conv.lastMessage) {\n          try {\n            normalizedLastMessage = this.normalizeMessage(conv.lastMessage);\n          } catch (error) {\n            this.logger.warn('[MessageService] Error normalizing last message, using null', error);\n          }\n        }\n        // Construire la conversation normalisée\n        const normalizedConversation = {\n          ...conv,\n          _id: conv.id || conv._id,\n          id: conv.id || conv._id,\n          participants: normalizedParticipants,\n          messages: normalizedMessages,\n          lastMessage: normalizedLastMessage,\n          unreadCount: conv.unreadCount || 0,\n          isGroup: !!conv.isGroup,\n          createdAt: this.normalizeDate(conv.createdAt),\n          updatedAt: this.normalizeDate(conv.updatedAt)\n        };\n        this.logger.debug('[MessageService] Conversation normalized successfully', {\n          conversationId: normalizedConversation.id,\n          participantCount: normalizedParticipants.length,\n          messageCount: normalizedMessages.length\n        });\n        return normalizedConversation;\n      } catch (error) {\n        this.logger.error('[MessageService] Error normalizing conversation:', error instanceof Error ? error : new Error(String(error)), conv);\n        throw new Error(`Failed to normalize conversation: ${error instanceof Error ? error.message : String(error)}`);\n      }\n    }\n    normalizeDate(date) {\n      if (!date) return new Date();\n      try {\n        return typeof date === 'string' ? new Date(date) : date;\n      } catch (error) {\n        this.logger.warn(`Failed to parse date: ${date}`, error);\n        return new Date();\n      }\n    }\n    // Méthode sécurisée pour créer une date à partir d'une valeur potentiellement undefined\n    safeDate(date) {\n      if (!date) return new Date();\n      try {\n        return typeof date === 'string' ? new Date(date) : date;\n      } catch (error) {\n        this.logger.warn(`Failed to create safe date: ${date}`, error);\n        return new Date();\n      }\n    }\n    normalizeNotification(notification) {\n      this.logger.debug('MessageService', 'Normalizing notification', notification);\n      if (!notification) {\n        this.logger.error('MessageService', 'Notification is null or undefined');\n        throw new Error('Notification is required');\n      }\n      // Vérifier et normaliser l'ID\n      const notificationId = notification.id || notification._id;\n      if (!notificationId) {\n        this.logger.error('MessageService', 'Notification ID is missing', notification);\n        throw new Error('Notification ID is required');\n      }\n      if (!notification.timestamp) {\n        this.logger.warn('MessageService', 'Notification timestamp is missing, using current time', notification);\n        notification.timestamp = new Date();\n      }\n      try {\n        const normalized = {\n          ...notification,\n          _id: notificationId,\n          id: notificationId,\n          timestamp: new Date(notification.timestamp),\n          ...(notification.senderId && {\n            senderId: this.normalizeSender(notification.senderId)\n          }),\n          ...(notification.message && {\n            message: this.normalizeNotMessage(notification.message)\n          })\n        };\n        this.logger.debug('MessageService', 'Normalized notification result', normalized);\n        return normalized;\n      } catch (error) {\n        this.logger.error('MessageService', 'Error in normalizeNotification', error);\n        throw error;\n      }\n    }\n    normalizeSender(sender) {\n      return {\n        id: sender.id,\n        username: sender.username,\n        ...(sender.image && {\n          image: sender.image\n        })\n      };\n    }\n    /**\n     * Normalise un message de notification\n     * @param message Message à normaliser\n     * @returns Message normalisé\n     */\n    normalizeNotMessage(message) {\n      if (!message) return null;\n      return {\n        id: message.id || message._id,\n        content: message.content || '',\n        type: message.type || 'TEXT',\n        timestamp: this.safeDate(message.timestamp),\n        attachments: message.attachments || [],\n        ...(message.sender && {\n          sender: this.normalizeSender(message.sender)\n        })\n      };\n    }\n    /**\n     * Met à jour le cache de notifications avec une ou plusieurs notifications\n     * @param notifications Notification(s) à ajouter au cache\n     * @param skipDuplicates Si true, ignore les notifications déjà présentes dans le cache\n     */\n    updateCache(notifications, skipDuplicates = true) {\n      const notificationArray = Array.isArray(notifications) ? notifications : [notifications];\n      this.logger.debug('MessageService', `Updating notification cache with ${notificationArray.length} notifications`);\n      if (notificationArray.length === 0) {\n        this.logger.warn('MessageService', 'No notifications to update in cache');\n        return;\n      }\n      // Vérifier si les notifications ont des IDs valides\n      const validNotifications = notificationArray.filter(notif => notif && (notif.id || notif._id));\n      if (validNotifications.length !== notificationArray.length) {\n        this.logger.warn('MessageService', `Found ${notificationArray.length - validNotifications.length} notifications without valid IDs`);\n      }\n      let addedCount = 0;\n      let skippedCount = 0;\n      // Traiter chaque notification\n      validNotifications.forEach((notif, index) => {\n        try {\n          // S'assurer que la notification a un ID\n          const notifId = notif.id || notif._id;\n          if (!notifId) {\n            this.logger.error('MessageService', 'Notification without ID:', notif);\n            return;\n          }\n          // Normaliser la notification\n          const normalized = this.normalizeNotification(notif);\n          // Vérifier si cette notification existe déjà dans le cache\n          if (skipDuplicates && this.notificationCache.has(normalized.id)) {\n            this.logger.debug('MessageService', `Notification ${normalized.id} already exists in cache, skipping`);\n            skippedCount++;\n            return;\n          }\n          // Ajouter au cache\n          this.notificationCache.set(normalized.id, normalized);\n          addedCount++;\n          this.logger.debug('MessageService', `Added notification ${normalized.id} to cache`);\n        } catch (error) {\n          this.logger.error('MessageService', `Error processing notification ${index + 1}:`, error);\n        }\n      });\n      this.logger.debug('MessageService', `Cache update complete: ${addedCount} added, ${skippedCount} skipped, total: ${this.notificationCache.size}`);\n      // Mettre à jour les observables et sauvegarder\n      this.refreshNotificationObservables();\n    }\n    /**\n     * Met à jour les observables de notifications et sauvegarde dans le localStorage\n     * OPTIMISÉ: Trie les notifications par date (plus récentes en premier)\n     */\n    refreshNotificationObservables() {\n      const allNotifications = Array.from(this.notificationCache.values());\n      // 🚀 TRI OPTIMISÉ: Les notifications les plus récentes en premier\n      const sortedNotifications = this.sortNotificationsByDate(allNotifications);\n      this.logger.debug(`📊 SORTED: ${sortedNotifications.length} notifications triées par date (plus récentes en premier)`);\n      this.notifications.next(sortedNotifications);\n      this.updateUnreadCount();\n      this.saveNotificationsToLocalStorage();\n    }\n    /**\n     * Met à jour le compteur de notifications non lues\n     */\n    updateUnreadCount() {\n      const allNotifications = Array.from(this.notificationCache.values());\n      const unreadNotifications = allNotifications.filter(n => !n.isRead);\n      const count = unreadNotifications.length;\n      // Forcer la mise à jour dans la zone Angular\n      this.zone.run(() => {\n        this.notificationCount.next(count);\n        // Émettre un événement global pour forcer la mise à jour du layout\n        window.dispatchEvent(new CustomEvent('notificationCountChanged', {\n          detail: {\n            count\n          }\n        }));\n      });\n    }\n    /**\n     * Met à jour le cache avec une seule notification (méthode simplifiée)\n     * @param notification Notification à ajouter\n     */\n    updateNotificationCache(notification) {\n      this.updateCache(notification, true);\n    }\n    /**\n     * Met à jour le statut de lecture des notifications\n     * @param ids IDs des notifications à mettre à jour\n     * @param isRead Nouveau statut de lecture\n     */\n    updateNotificationStatus(ids, isRead) {\n      ids.forEach(id => {\n        const notif = this.notificationCache.get(id);\n        if (notif) {\n          this.notificationCache.set(id, {\n            ...notif,\n            isRead,\n            readAt: isRead ? new Date().toISOString() : undefined\n          });\n        }\n      });\n      this.refreshNotificationObservables();\n    }\n    /**\n     * Méthode générique pour supprimer des notifications du cache local\n     * @param notificationIds IDs des notifications à supprimer\n     * @returns Nombre de notifications supprimées\n     */\n    removeNotificationsFromCache(notificationIds) {\n      console.log('🗑️ REMOVE FROM CACHE: Starting removal of', notificationIds.length, 'notifications');\n      console.log('🗑️ REMOVE FROM CACHE: Cache size before:', this.notificationCache.size);\n      let removedCount = 0;\n      notificationIds.forEach(id => {\n        if (this.notificationCache.has(id)) {\n          console.log('🗑️ REMOVE FROM CACHE: Removing notification:', id);\n          this.notificationCache.delete(id);\n          removedCount++;\n        } else {\n          console.log('🗑️ REMOVE FROM CACHE: Notification not found in cache:', id);\n        }\n      });\n      console.log('🗑️ REMOVE FROM CACHE: Removed', removedCount, 'notifications');\n      console.log('🗑️ REMOVE FROM CACHE: Cache size after:', this.notificationCache.size);\n      if (removedCount > 0) {\n        console.log('🗑️ REMOVE FROM CACHE: Refreshing observables...');\n        this.refreshNotificationObservables();\n      }\n      return removedCount;\n    }\n    /**\n     * Méthode générique pour gérer les erreurs de suppression\n     * @param error Erreur survenue\n     * @param operation Nom de l'opération\n     * @param fallbackResponse Réponse de fallback en cas d'erreur\n     */\n    handleDeletionError(error, operation, fallbackResponse) {\n      this.logger.error('MessageService', `Erreur lors de ${operation}:`, error);\n      return of(fallbackResponse);\n    }\n    // Typing indicators\n    startTyping(conversationId) {\n      const userId = this.getCurrentUserId();\n      if (!userId) {\n        this.logger.warn('MessageService', 'Cannot start typing: no user ID');\n        return of(false);\n      }\n      return this.apollo.mutate({\n        mutation: START_TYPING_MUTATION,\n        variables: {\n          input: {\n            conversationId,\n            userId\n          }\n        }\n      }).pipe(map(result => result.data?.startTyping || false), catchError(error => {\n        this.logger.error('MessageService', 'Error starting typing indicator', error);\n        return throwError(() => new Error('Failed to start typing indicator'));\n      }));\n    }\n    stopTyping(conversationId) {\n      const userId = this.getCurrentUserId();\n      if (!userId) {\n        this.logger.warn('MessageService', 'Cannot stop typing: no user ID');\n        return of(false);\n      }\n      return this.apollo.mutate({\n        mutation: STOP_TYPING_MUTATION,\n        variables: {\n          input: {\n            conversationId,\n            userId\n          }\n        }\n      }).pipe(map(result => result.data?.stopTyping || false), catchError(error => {\n        this.logger.error('MessageService', 'Error stopping typing indicator', error);\n        return throwError(() => new Error('Failed to stop typing indicator'));\n      }));\n    }\n    // ========================================\n    // MÉTHODE SENDMESSAGE MANQUANTE\n    // ========================================\n    /**\n     * Envoie un message (texte, fichier, audio, etc.)\n     * @param receiverId ID du destinataire\n     * @param content Contenu du message (texte)\n     * @param file Fichier à envoyer (optionnel)\n     * @param messageType Type de message (TEXT, AUDIO, IMAGE, etc.)\n     * @param conversationId ID de la conversation\n     * @returns Observable avec le message envoyé\n     */\n    sendMessage(receiverId, content, file, messageType = 'TEXT', conversationId) {\n      console.log('🚀 [MessageService] sendMessage called with:', {\n        receiverId,\n        content: content?.substring(0, 50),\n        hasFile: !!file,\n        fileName: file?.name,\n        fileType: file?.type,\n        fileSize: file?.size,\n        messageType,\n        conversationId\n      });\n      if (!receiverId) {\n        const error = new Error('Receiver ID is required');\n        console.error('❌ [MessageService] sendMessage error:', error);\n        return throwError(() => error);\n      }\n      // Préparer les variables pour la mutation\n      const variables = {\n        receiverId,\n        content: content || '',\n        type: messageType\n      };\n      // Ajouter l'ID de conversation si fourni\n      if (conversationId) {\n        variables.conversationId = conversationId;\n      }\n      // Si un fichier est fourni, l'ajouter aux variables\n      if (file) {\n        variables.file = file;\n        console.log('📁 [MessageService] Adding file to mutation:', {\n          name: file.name,\n          type: file.type,\n          size: file.size\n        });\n      }\n      console.log('📤 [MessageService] Sending mutation with variables:', variables);\n      return this.apollo.mutate({\n        mutation: SEND_MESSAGE_MUTATION,\n        variables,\n        context: {\n          useMultipart: !!file // Utiliser multipart si un fichier est présent\n        }\n      }).pipe(map(result => {\n        console.log('✅ [MessageService] sendMessage mutation result:', result);\n        if (!result.data?.sendMessage) {\n          throw new Error('No message data received from server');\n        }\n        const message = result.data.sendMessage;\n        console.log('📨 [MessageService] Message sent successfully:', {\n          id: message.id,\n          type: message.type,\n          content: message.content?.substring(0, 50),\n          hasAttachments: !!message.attachments?.length\n        });\n        // Normaliser le message reçu\n        const normalizedMessage = this.normalizeMessage(message);\n        console.log('🔧 [MessageService] Message normalized:', normalizedMessage);\n        return normalizedMessage;\n      }), catchError(error => {\n        console.error('❌ [MessageService] sendMessage error:', error);\n        this.logger.error('Error sending message:', error);\n        // Fournir un message d'erreur plus spécifique\n        let errorMessage = \"Erreur lors de l'envoi du message\";\n        if (error.networkError) {\n          errorMessage = 'Erreur de connexion réseau';\n        } else if (error.graphQLErrors?.length > 0) {\n          errorMessage = error.graphQLErrors[0].message || errorMessage;\n        }\n        return throwError(() => new Error(errorMessage));\n      }));\n    }\n    // ========================================\n    // MÉTHODES UTILITAIRES CONSOLIDÉES\n    // ========================================\n    /**\n     * Formate l'heure d'un message\n     */\n    formatMessageTime(timestamp) {\n      if (!timestamp) return 'Unknown time';\n      try {\n        const date = timestamp instanceof Date ? timestamp : new Date(timestamp);\n        return date.toLocaleTimeString([], {\n          hour: '2-digit',\n          minute: '2-digit',\n          hour12: false\n        });\n      } catch (error) {\n        return 'Invalid time';\n      }\n    }\n    /**\n     * Formate la dernière activité d'un utilisateur\n     */\n    formatLastActive(lastActive) {\n      if (!lastActive) return 'Offline';\n      const lastActiveDate = lastActive instanceof Date ? lastActive : new Date(lastActive);\n      const now = new Date();\n      const diffHours = Math.abs(now.getTime() - lastActiveDate.getTime()) / (1000 * 60 * 60);\n      if (diffHours < 24) {\n        return `Active ${lastActiveDate.toLocaleTimeString([], {\n          hour: '2-digit',\n          minute: '2-digit'\n        })}`;\n      }\n      return `Active ${lastActiveDate.toLocaleDateString()}`;\n    }\n    /**\n     * Formate la date d'un message\n     */\n    formatMessageDate(timestamp) {\n      if (!timestamp) return 'Unknown date';\n      try {\n        const date = timestamp instanceof Date ? timestamp : new Date(timestamp);\n        const today = new Date();\n        if (date.toDateString() === today.toDateString()) {\n          return date.toLocaleTimeString([], {\n            hour: '2-digit',\n            minute: '2-digit'\n          });\n        }\n        const yesterday = new Date(today);\n        yesterday.setDate(yesterday.getDate() - 1);\n        if (date.toDateString() === yesterday.toDateString()) {\n          return `LUN., ${date.toLocaleTimeString([], {\n            hour: '2-digit',\n            minute: '2-digit'\n          })}`;\n        }\n        const day = date.toLocaleDateString('fr-FR', {\n          weekday: 'short'\n        }).toUpperCase();\n        return `${day}., ${date.toLocaleTimeString([], {\n          hour: '2-digit',\n          minute: '2-digit'\n        })}`;\n      } catch (error) {\n        return 'Invalid date';\n      }\n    }\n    /**\n     * Détermine si un en-tête de date doit être affiché\n     */\n    shouldShowDateHeader(messages, index) {\n      if (index === 0) return true;\n      try {\n        const currentMsg = messages[index];\n        const prevMsg = messages[index - 1];\n        if (!currentMsg?.timestamp || !prevMsg?.timestamp) return true;\n        const currentDate = this.getDateFromTimestamp(currentMsg.timestamp);\n        const prevDate = this.getDateFromTimestamp(prevMsg.timestamp);\n        return currentDate !== prevDate;\n      } catch (error) {\n        return false;\n      }\n    }\n    getDateFromTimestamp(timestamp) {\n      if (!timestamp) return 'unknown-date';\n      try {\n        return (timestamp instanceof Date ? timestamp : new Date(timestamp)).toDateString();\n      } catch (error) {\n        return 'invalid-date';\n      }\n    }\n    /**\n     * Obtient l'icône d'un fichier selon son type MIME\n     */\n    getFileIcon(mimeType) {\n      if (!mimeType) return 'fa-file';\n      if (mimeType.startsWith('image/')) return 'fa-image';\n      if (mimeType.includes('pdf')) return 'fa-file-pdf';\n      if (mimeType.includes('word') || mimeType.includes('msword')) return 'fa-file-word';\n      if (mimeType.includes('excel')) return 'fa-file-excel';\n      if (mimeType.includes('powerpoint')) return 'fa-file-powerpoint';\n      if (mimeType.includes('audio')) return 'fa-file-audio';\n      if (mimeType.includes('video')) return 'fa-file-video';\n      if (mimeType.includes('zip') || mimeType.includes('compressed')) return 'fa-file-archive';\n      return 'fa-file';\n    }\n    /**\n     * Obtient le type d'un fichier selon son type MIME\n     */\n    getFileType(mimeType) {\n      if (!mimeType) return 'File';\n      const typeMap = {\n        'image/': 'Image',\n        'application/pdf': 'PDF',\n        'application/msword': 'Word Doc',\n        'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'Word Doc',\n        'application/vnd.ms-excel': 'Excel',\n        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'Excel',\n        'application/vnd.ms-powerpoint': 'PowerPoint',\n        'application/vnd.openxmlformats-officedocument.presentationml.presentation': 'PowerPoint',\n        'audio/': 'Audio',\n        'video/': 'Video',\n        'application/zip': 'ZIP Archive',\n        'application/x-rar-compressed': 'RAR Archive'\n      };\n      for (const [key, value] of Object.entries(typeMap)) {\n        if (mimeType.includes(key)) return value;\n      }\n      return 'File';\n    }\n    /**\n     * Vérifie si un message contient une image\n     */\n    hasImage(message) {\n      if (!message || !message.attachments || message.attachments.length === 0) {\n        return false;\n      }\n      const attachment = message.attachments[0];\n      if (!attachment || !attachment.type) {\n        return false;\n      }\n      const type = attachment.type.toString();\n      return type === 'IMAGE' || type === 'image';\n    }\n    /**\n     * Vérifie si le message est un message vocal\n     */\n    isVoiceMessage(message) {\n      if (!message) return false;\n      // Vérifier le type du message\n      if (message.type === MessageType.VOICE_MESSAGE || message.type === MessageType.VOICE_MESSAGE) {\n        return true;\n      }\n      // Vérifier les pièces jointes\n      if (message.attachments && message.attachments.length > 0) {\n        return message.attachments.some(att => {\n          const type = att.type?.toString();\n          return type === 'VOICE_MESSAGE' || type === 'voice_message' || message.metadata?.isVoiceMessage && (type === 'AUDIO' || type === 'audio');\n        });\n      }\n      // Vérifier les métadonnées\n      return !!message.metadata?.isVoiceMessage;\n    }\n    /**\n     * Récupère l'URL du message vocal\n     */\n    getVoiceMessageUrl(message) {\n      if (!message || !message.attachments || message.attachments.length === 0) {\n        return '';\n      }\n      const voiceAttachment = message.attachments.find(att => {\n        const type = att.type?.toString();\n        return type === 'VOICE_MESSAGE' || type === 'voice_message' || type === 'AUDIO' || type === 'audio';\n      });\n      return voiceAttachment?.url || '';\n    }\n    /**\n     * Récupère la durée du message vocal\n     */\n    getVoiceMessageDuration(message) {\n      if (!message) return 0;\n      // Essayer d'abord de récupérer la durée depuis les métadonnées\n      if (message.metadata?.duration) {\n        return message.metadata.duration;\n      }\n      // Sinon, essayer de récupérer depuis les pièces jointes\n      if (message.attachments && message.attachments.length > 0) {\n        const voiceAttachment = message.attachments.find(att => {\n          const type = att.type?.toString();\n          return type === 'VOICE_MESSAGE' || type === 'voice_message' || type === 'AUDIO' || type === 'audio';\n        });\n        if (voiceAttachment && voiceAttachment.duration) {\n          return voiceAttachment.duration;\n        }\n      }\n      return 0;\n    }\n    /**\n     * Génère la hauteur des barres de la forme d'onde moderne\n     */\n    getVoiceBarHeight(index) {\n      const pattern = [8, 12, 6, 15, 10, 18, 7, 14, 9, 16, 5, 13, 11, 17, 8, 12, 6, 15, 10, 18];\n      return pattern[index % pattern.length];\n    }\n    /**\n     * Formate la durée du message vocal en format MM:SS\n     */\n    formatVoiceDuration(seconds) {\n      if (!seconds || seconds === 0) {\n        return '0:00';\n      }\n      const minutes = Math.floor(seconds / 60);\n      const remainingSeconds = Math.floor(seconds % 60);\n      return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n    }\n    /**\n     * Obtient l'URL de l'image en toute sécurité\n     */\n    getImageUrl(message) {\n      if (!message || !message.attachments || message.attachments.length === 0) {\n        return '';\n      }\n      const attachment = message.attachments[0];\n      return attachment?.url || '';\n    }\n    /**\n     * Détermine le type d'un message\n     */\n    getMessageType(message) {\n      if (!message) return MessageType.TEXT;\n      try {\n        if (message.type) {\n          const msgType = message.type.toString();\n          if (msgType === 'text' || msgType === 'TEXT') {\n            return MessageType.TEXT;\n          } else if (msgType === 'image' || msgType === 'IMAGE') {\n            return MessageType.IMAGE;\n          } else if (msgType === 'file' || msgType === 'FILE') {\n            return MessageType.FILE;\n          } else if (msgType === 'audio' || msgType === 'AUDIO') {\n            return MessageType.AUDIO;\n          } else if (msgType === 'video' || msgType === 'VIDEO') {\n            return MessageType.VIDEO;\n          } else if (msgType === 'system' || msgType === 'SYSTEM') {\n            return MessageType.SYSTEM;\n          }\n        }\n        if (message.attachments?.length) {\n          const attachment = message.attachments[0];\n          if (attachment && attachment.type) {\n            const attachmentTypeStr = attachment.type.toString();\n            if (attachmentTypeStr === 'image' || attachmentTypeStr === 'IMAGE') {\n              return MessageType.IMAGE;\n            } else if (attachmentTypeStr === 'file' || attachmentTypeStr === 'FILE') {\n              return MessageType.FILE;\n            } else if (attachmentTypeStr === 'audio' || attachmentTypeStr === 'AUDIO') {\n              return MessageType.AUDIO;\n            } else if (attachmentTypeStr === 'video' || attachmentTypeStr === 'VIDEO') {\n              return MessageType.VIDEO;\n            }\n          }\n          return MessageType.FILE;\n        }\n        return MessageType.TEXT;\n      } catch (error) {\n        return MessageType.TEXT;\n      }\n    }\n    /**\n     * Retourne la liste des emojis communs\n     */\n    getCommonEmojis() {\n      return ['😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰', '😘', '😗', '😙', '😚', '😋', '😛', '😝', '😜', '🤪', '🤨', '🧐', '🤓', '😎', '🤩', '😏', '😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣', '😖', '😫', '😩', '🥺', '😢', '😭', '😤', '😠', '😡', '🤬', '🤯', '😳', '🥵', '🥶', '😱', '😨', '😰', '😥', '😓', '🤗', '🤔', '👍', '👎', '👏', '🙌', '👐', '🤲', '🤝', '🙏', '✌️', '🤞', '❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '💔', '💯', '💢'];\n    }\n    /**\n     * Obtient les classes CSS pour un message\n     */\n    getMessageTypeClass(message, currentUserId) {\n      if (!message) {\n        return 'bg-gray-100 rounded-lg px-4 py-2';\n      }\n      try {\n        const isCurrentUser = message.sender?.id === currentUserId || message.sender?._id === currentUserId || message.senderId === currentUserId;\n        const baseClass = isCurrentUser ? 'bg-blue-500 text-white rounded-2xl rounded-br-sm' : 'bg-gray-200 text-gray-800 rounded-2xl rounded-bl-sm';\n        const messageType = this.getMessageType(message);\n        if (message.attachments && message.attachments.length > 0) {\n          const attachment = message.attachments[0];\n          if (attachment && attachment.type) {\n            const attachmentTypeStr = attachment.type.toString();\n            if (attachmentTypeStr === 'IMAGE' || attachmentTypeStr === 'image') {\n              return `p-1 max-w-xs`;\n            } else if (attachmentTypeStr === 'FILE' || attachmentTypeStr === 'file') {\n              return `${baseClass} p-3`;\n            }\n          }\n        }\n        // Les vérifications de type sont déjà faites avec les attachments ci-dessus\n        return `${baseClass} px-4 py-3 whitespace-normal break-words min-w-[120px]`;\n      } catch (error) {\n        return 'bg-gray-100 rounded-lg px-4 py-2 whitespace-normal break-words';\n      }\n    }\n    // ========================================\n    // APPELS WEBRTC - DÉLÉGUÉS AU CALLSERVICE\n    // ========================================\n    // Note: Les méthodes d'appel ont été déplacées vers CallService\n    // pour éviter la duplication de code et centraliser la logique\n    // destroy\n    cleanupSubscriptions() {\n      this.subscriptions.forEach(sub => sub.unsubscribe());\n      this.subscriptions = [];\n      if (this.cleanupInterval) {\n        clearInterval(this.cleanupInterval);\n      }\n      this.notificationCache.clear();\n      this.logger.debug('NotificationService destroyed');\n    }\n    /**\n     * Joue un son de notification avec type spécifique\n     */\n    playNotificationSoundWithType(type = 'message') {\n      console.log(`MessageService: Tentative de lecture du son de notification (${type})`);\n      if (this.muted) {\n        console.log('MessageService: Son désactivé, notification ignorée');\n        return;\n      }\n      try {\n        // Utiliser différentes mélodies selon le type\n        const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n        switch (type) {\n          case 'call':\n            this.playNotificationMelody4(audioContext); // Triple note pour les appels\n            break;\n          case 'success':\n            this.playNotificationMelody2(audioContext); // Mélodie montante pour le succès\n            break;\n          case 'error':\n            this.playNotificationMelody3(audioContext); // Mélodie descendante pour les erreurs\n            break;\n          default:\n            this.playNotificationMelody1(audioContext);\n          // Mélodie douce pour les messages\n        }\n\n        console.log(`MessageService: Son de notification ${type} joué avec succès`);\n      } catch (error) {\n        console.error(`MessageService: Erreur lors de la lecture du son ${type}:`, error);\n        // Fallback vers les fichiers audio\n        try {\n          let soundFile = 'assets/sounds/notification.mp3';\n          if (type === 'call') {\n            soundFile = 'assets/sounds/call-end.mp3';\n          }\n          const audio = new Audio(soundFile);\n          audio.volume = type === 'call' ? 0.7 : 0.5;\n          audio.play().catch(err => console.error('Erreur fallback audio:', err));\n        } catch (audioError) {\n          console.error('Erreur fallback audio:', audioError);\n        }\n      }\n    }\n    /**\n     * Envoie un message vocal (simulation pour l'instant)\n     */\n    sendVoiceMessage(recipientId, formData) {\n      console.log(\"MessageService: Envoi d'un message vocal\");\n      // Pour l'instant, on simule l'envoi d'un message vocal\n      // TODO: Implémenter la vraie logique avec GraphQL\n      const mockMessage = {\n        id: Date.now().toString(),\n        senderId: 'current-user',\n        receiverId: recipientId,\n        content: 'Message vocal',\n        type: MessageType.VOICE_MESSAGE,\n        timestamp: new Date(),\n        attachments: [{\n          name: 'voice-message.webm',\n          url: URL.createObjectURL(formData.get('audio')),\n          type: 'audio/webm',\n          size: formData.get('audio').size,\n          duration: parseInt(formData.get('duration')) || 0\n        }]\n      };\n      return of(mockMessage).pipe(tap(response => {\n        console.log('MessageService: Message vocal envoyé avec succès', response);\n      }), catchError(error => {\n        console.error(\"MessageService: Erreur lors de l'envoi du message vocal:\", error);\n        return throwError(() => error);\n      }));\n    }\n    /**\n     * Envoie un message de localisation (simulation pour l'instant)\n     */\n    sendLocationMessage(recipientId, locationData) {\n      console.log(\"MessageService: Envoi d'un message de localisation\");\n      // Pour l'instant, on simule l'envoi d'un message de localisation\n      // TODO: Implémenter la vraie logique avec GraphQL\n      const mockMessage = {\n        id: Date.now().toString(),\n        senderId: 'current-user',\n        receiverId: recipientId,\n        content: `Localisation partagée: ${locationData.address || 'Position GPS'}`,\n        type: 'location',\n        timestamp: new Date(),\n        locationData: {\n          latitude: locationData.latitude,\n          longitude: locationData.longitude,\n          address: locationData.address,\n          mapUrl: locationData.mapUrl\n        }\n      };\n      return of(mockMessage).pipe(tap(response => {\n        console.log('MessageService: Message de localisation envoyé avec succès', response);\n      }), catchError(error => {\n        console.error(\"MessageService: Erreur lors de l'envoi du message de localisation:\", error);\n        return throwError(() => error);\n      }));\n    }\n    ngOnDestroy() {\n      this.cleanupSubscriptions();\n    }\n    static {\n      this.ɵfac = function MessageService_Factory(t) {\n        return new (t || MessageService)(i0.ɵɵinject(i1.Apollo), i0.ɵɵinject(i2.LoggerService), i0.ɵɵinject(i0.NgZone));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: MessageService,\n        factory: MessageService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return MessageService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}