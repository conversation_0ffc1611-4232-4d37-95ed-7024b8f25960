{"ast": null, "code": "import { map } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./services/logger.service\";\nimport * as i2 from \"./services/theme.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/router\";\nconst _c0 = function (a0) {\n  return {\n    dark: a0\n  };\n};\nexport let AppComponent = /*#__PURE__*/(() => {\n  class AppComponent {\n    constructor(logger, themeService) {\n      this.logger = logger;\n      this.themeService = themeService;\n      this.title = 'frontend';\n      // Utiliser le nouveau système de thème\n      this.isDarkMode$ = this.themeService.currentTheme$.pipe(map(theme => theme.name === 'dark'));\n    }\n    ngOnInit() {\n      // Activer les logs en développement\n      this.logger.setLogsEnabled(true);\n      // Activer les logs pour tous les composants importants\n      this.logger.enableComponentLogs('MessageService');\n      this.logger.enableComponentLogs('MessageChat');\n      this.logger.enableComponentLogs('CallService');\n      this.logger.enableComponentLogs('IncomingCall');\n      this.logger.enableComponentLogs('ActiveCall');\n      this.logger.enableComponentLogs('VoiceMessage');\n      this.logger.enableComponentLogs('NotificationService');\n      console.log('🚀 [App] Application initialized with full logging enabled');\n      this.logger.info('App', 'Application started successfully');\n    }\n    static {\n      this.ɵfac = function AppComponent_Factory(t) {\n        return new (t || AppComponent)(i0.ɵɵdirectiveInject(i1.LoggerService), i0.ɵɵdirectiveInject(i2.ThemeService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: AppComponent,\n        selectors: [[\"app-root\"]],\n        decls: 4,\n        vars: 5,\n        consts: [[1, \"app-container\", 3, \"ngClass\"]],\n        template: function AppComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0);\n            i0.ɵɵpipe(1, \"async\");\n            i0.ɵɵelement(2, \"router-outlet\")(3, \"app-test-page\");\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(3, _c0, i0.ɵɵpipeBind1(1, 1, ctx.isDarkMode$)));\n          }\n        },\n        dependencies: [i3.NgClass, i4.RouterOutlet, i3.AsyncPipe],\n        styles: [\".app-container[_ngcontent-%COMP%]{min-height:100vh;width:100%}.dark[_ngcontent-%COMP%]{color-scheme:dark}\"]\n      });\n    }\n  }\n  return AppComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}