{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let AuthService = /*#__PURE__*/(() => {\n  class AuthService {\n    constructor(http) {\n      this.http = http;\n      this.AUTH_API = 'http://localhost:3000/api/auth';\n      this.ADMIN_API = 'http://localhost:3000/api/admin';\n    }\n    // Auth endpoints\n    signup(data) {\n      return this.http.post(`${this.AUTH_API}/signup`, data);\n    }\n    verifyEmail(data) {\n      return this.http.post(`${this.AUTH_API}/verify-email`, data);\n    }\n    login(data) {\n      return this.http.post(`${this.AUTH_API}/login`, data);\n    }\n    forgotPassword(email) {\n      return this.http.post(`${this.AUTH_API}/forgot-password`, {\n        email\n      });\n    }\n    resetPassword(data) {\n      return this.http.post(`${this.AUTH_API}/reset-password`, data);\n    }\n    getProfile(token) {\n      return this.http.get(`${this.AUTH_API}/profile`, {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n    }\n    updateProfile(formData, token) {\n      return this.http.put(`${this.AUTH_API}/update-profile`, formData, {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n    }\n    removeProfileImage(token) {\n      return this.http.delete(`${this.AUTH_API}/remove-profile-image`, {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n    }\n    changePassword(data, token) {\n      return this.http.put(`${this.AUTH_API}/change-password`, data, {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n    }\n    // Admin endpoints\n    getAllUsers(token) {\n      return this.http.get(`${this.ADMIN_API}/users`, {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n    }\n    updateUserRole(userId, role, token) {\n      return this.http.put(`${this.ADMIN_API}/users/${userId}/role`, {\n        role\n      }, {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n    }\n    deleteUser(userId, token) {\n      return this.http.delete(`${this.ADMIN_API}/users/${userId}`, {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n    }\n    toggleUserActivation(userId, isActive, token) {\n      return this.http.put(`${this.ADMIN_API}/users/${userId}/activation`, {\n        isActive\n      }, {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n    }\n    getUserById(userId, token) {\n      return this.http.get(`${this.ADMIN_API}/users/${userId}`, {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n    }\n    getCurrentUser() {\n      const user = localStorage.getItem('user');\n      return user ? JSON.parse(user) : null;\n    }\n    getUserRole() {\n      const user = JSON.parse(localStorage.getItem('user') || '{}');\n      return user?.role || '';\n    }\n    isAdmin() {\n      return this.getUserRole() === 'admin';\n    }\n    logout() {\n      localStorage.removeItem('token');\n      localStorage.removeItem('user');\n    }\n    resendCode(email) {\n      return this.http.post(`${this.AUTH_API}/resend-code`, {\n        email\n      });\n    }\n    static {\n      this.ɵfac = function AuthService_Factory(t) {\n        return new (t || AuthService)(i0.ɵɵinject(i1.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: AuthService,\n        factory: AuthService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return AuthService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}