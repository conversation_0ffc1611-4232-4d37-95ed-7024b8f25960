import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FormsModule } from "@angular/forms";

// Composants principaux que nous avons créés
import { NotificationDisplayComponent } from "./notification-display/notification-display.component";
import { MessageChatComponent } from "./message-chat/message-chat.component";
import { TestPageComponent } from "./test-page/test-page.component";

@NgModule({
  declarations: [
    // Composants principaux
    NotificationDisplayComponent,
    MessageChatComponent,
    TestPageComponent,
  ],
  imports: [CommonModule, FormsModule],
  exports: [
    // Composants principaux
    NotificationDisplayComponent,
    MessageChatComponent,
    TestPageComponent,
  ],
})
export class SharedComponentsModule {}
