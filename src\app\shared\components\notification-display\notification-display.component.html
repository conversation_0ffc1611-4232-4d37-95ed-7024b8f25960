<!-- Conteneur de notifications -->
<div class="notification-container position-top-right">
  <!-- Liste des notifications -->
  <div
    *ngFor="
      let notification of notifications$ | async;
      trackBy: trackByNotificationId
    "
    [class]="getNotificationClasses(notification)"
    (click)="onNotificationClick(notification)">
    <!-- En-tête de la notification -->
    <div class="notification-header">
      <!-- Icône -->
      <div
        class="notification-icon"
        *ngIf="notification.icon">
        <i [class]="notification.icon"></i>
      </div>

      <!-- Titre et timestamp -->
      <div class="notification-title-section">
        <h4 class="notification-title">{{ notification.title }}</h4>
        <span class="notification-timestamp">{{
          formatTimestamp(notification.timestamp)
        }}</span>
      </div>

      <!-- Bouton de fermeture -->
      <button
        *ngIf="notification.closable"
        class="notification-close"
        (click)="onDismiss(notification, $event)"
        title="Fermer">
        <i class="fas fa-times"></i>
      </button>
    </div>

    <!-- Contenu principal -->
    <div class="notification-content">
      <!-- Message -->
      <p class="notification-message">{{ notification.message }}</p>

      <!-- Image si présente -->
      <div
        *ngIf="notification.image"
        class="notification-image">
        <img
          [src]="notification.image"
          [alt]="notification.title" />
      </div>

      <!-- Barre de progression si présente -->
      <div
        *ngIf="notification.progress !== undefined"
        class="notification-progress">
        <div class="progress-bar">
          <div
            class="progress-fill"
            [style.width.%]="getProgressWidth(notification)"></div>
        </div>
        <span class="progress-text">{{ notification.progress }}%</span>
      </div>
    </div>

    <!-- Actions -->
    <div
      *ngIf="notification.actions && notification.actions.length > 0"
      class="notification-actions">
      <button
        *ngFor="let action of notification.actions"
        [class]="'action-btn action-' + (action.style || 'secondary')"
        (click)="onActionClick(notification, action.id, $event)"
        [title]="action.title">
        <i
          *ngIf="action.icon"
          [class]="action.icon"></i>
        <span>{{ action.title }}</span>
      </button>
    </div>

    <!-- Indicateur de priorité -->
    <div
      *ngIf="
        notification.priority === 'high' || notification.priority === 'urgent'
      "
      class="priority-indicator"
      [class.urgent]="notification.priority === 'urgent'"></div>

    <!-- Badge si présent -->
    <div
      *ngIf="notification.badge"
      class="notification-badge">
      {{ notification.badge }}
    </div>
  </div>
</div>

<!-- Notification de test (pour développement) -->
<div
  class="notification-test-controls"
  *ngIf="false">
  <button
    (click)="testNotification('success')"
    class="test-btn success">
    Test Success
  </button>
  <button
    (click)="testNotification('error')"
    class="test-btn error">
    Test Error
  </button>
  <button
    (click)="testNotification('warning')"
    class="test-btn warning">
    Test Warning
  </button>
  <button
    (click)="testNotification('info')"
    class="test-btn info">
    Test Info
  </button>
  <button
    (click)="testTemplate()"
    class="test-btn template">
    Test Template
  </button>
</div>
