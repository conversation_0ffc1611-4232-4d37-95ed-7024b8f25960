{"ast": null, "code": "import { Subject, takeUntil } from 'rxjs';\nimport { map } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/authadmin.service\";\nimport * as i2 from \"src/app/services/authuser.service\";\nimport * as i3 from \"src/app/services/auth.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@app/services/theme.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/forms\";\nfunction AuthAdminLayoutComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48)(1, \"div\", 49)(2, \"div\", 34);\n    i0.ɵɵelement(3, \"i\", 50)(4, \"div\", 51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 52);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.messageFromRedirect, \" \");\n  }\n}\nfunction AuthAdminLayoutComponent_div_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵelement(1, \"i\", 54);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const _r2 = i0.ɵɵreference(62);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (_r2.errors == null ? null : _r2.errors[\"required\"]) ? \"Email requis\" : \"Format email invalide\", \" \");\n  }\n}\nfunction AuthAdminLayoutComponent_div_74_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵelement(1, \"i\", 54);\n    i0.ɵɵtext(2, \" Mot de passe requis \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AuthAdminLayoutComponent_div_75_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55);\n    i0.ɵɵelement(1, \"i\", 56);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r6.messageAuthError);\n  }\n}\nexport let AuthAdminLayoutComponent = /*#__PURE__*/(() => {\n  class AuthAdminLayoutComponent {\n    constructor(authAdminService, authUserService, authService, router, route, themeService) {\n      this.authAdminService = authAdminService;\n      this.authUserService = authUserService;\n      this.authService = authService;\n      this.router = router;\n      this.route = route;\n      this.themeService = themeService;\n      this.messageAuthError = '';\n      this.messageFromRedirect = '';\n      this.destroy$ = new Subject();\n      this.checkExistingAuth();\n      this.isDarkMode$ = this.themeService.currentTheme$.pipe(map(theme => theme.name === 'dark'));\n    }\n    ngOnInit() {\n      this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/admin/';\n      this.subscribeToQueryParams();\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n    }\n    checkExistingAuth() {\n      if (this.authUserService.userLoggedIn()) {\n        this.router.navigate(['/'], {\n          queryParams: {\n            message: \"Vous êtes déjà connecté en tant qu'utilisateur. Veuillez vous déconnecter d'abord.\"\n          }\n        });\n        return;\n      }\n      if (this.authAdminService.loggedIn()) {\n        this.router.navigateByUrl('/admin');\n      }\n    }\n    subscribeToQueryParams() {\n      this.route.queryParams.pipe(takeUntil(this.destroy$)).subscribe(params => {\n        this.messageFromRedirect = params['message'] || '';\n        this.clearMessageAfterDelay();\n      });\n    }\n    clearMessageAfterDelay() {\n      if (this.messageFromRedirect) {\n        setTimeout(() => this.messageFromRedirect = '', 5000);\n      }\n    }\n    loginAdmin(form) {\n      if (!form.valid) {\n        this.messageAuthError = 'Veuillez remplir correctement le formulaire.';\n        return;\n      }\n      const data = form.value;\n      this.authAdminService.login(data).subscribe({\n        next: response => {\n          this.handleLoginSuccess(response);\n        },\n        error: err => {\n          this.handleLoginError(err);\n        }\n      });\n    }\n    handleLoginSuccess(response) {\n      this.authAdminService.saveDataProfil(response.token);\n      this.router.navigate([this.returnUrl]);\n    }\n    handleLoginError(err) {\n      this.messageAuthError = err.error?.message || 'Une erreur est survenue lors de la connexion';\n      // Effacer le message d'erreur après 5 secondes\n      setTimeout(() => this.messageAuthError = '', 5000);\n    }\n    static {\n      this.ɵfac = function AuthAdminLayoutComponent_Factory(t) {\n        return new (t || AuthAdminLayoutComponent)(i0.ɵɵdirectiveInject(i1.AuthadminService), i0.ɵɵdirectiveInject(i2.AuthuserService), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i4.ActivatedRoute), i0.ɵɵdirectiveInject(i5.ThemeService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: AuthAdminLayoutComponent,\n        selectors: [[\"app-auth-admin-layout\"]],\n        decls: 81,\n        vars: 8,\n        consts: [[\"class\", \"fixed top-4 right-4 left-4 md:left-auto md:w-96 bg-white dark:bg-[#1e1e1e] border-l-4 border-[#4f5fad] dark:border-[#6d78c9] rounded-lg p-4 shadow-md dark:shadow-[0_4px_20px_rgba(0,0,0,0.3)] z-50 backdrop-blur-sm\", 4, \"ngIf\"], [1, \"min-h-screen\", \"main-grid-container\", \"flex\", \"items-center\", \"justify-center\", \"p-4\", \"relative\", \"overflow-hidden\"], [1, \"background-grid\"], [1, \"absolute\", \"top-0\", \"left-0\", \"w-full\", \"h-full\", \"overflow-hidden\", \"pointer-events-none\"], [1, \"absolute\", \"top-[10%]\", \"left-[5%]\", \"w-64\", \"h-64\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#4f5fad]/10\", \"to-transparent\", \"dark:from-[#6d78c9]/5\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"bottom-[10%]\", \"right-[5%]\", \"w-80\", \"h-80\", \"rounded-full\", \"bg-gradient-to-tl\", \"from-[#4f5fad]/10\", \"to-transparent\", \"dark:from-[#6d78c9]/5\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"w-full\", \"max-w-4xl\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"rounded-2xl\", \"shadow-xl\", \"dark:shadow-[0_10px_30px_rgba(0,0,0,0.3)]\", \"overflow-hidden\", \"backdrop-blur-sm\", \"relative\", \"z-10\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\"], [1, \"flex\", \"flex-col\", \"md:flex-row\"], [1, \"md:w-1/2\", \"bg-gradient-to-br\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#2a3052]\", \"dark:to-[#4f5fad]\", \"hidden\", \"md:flex\", \"items-center\", \"justify-center\", \"p-12\", \"relative\", \"overflow-hidden\"], [1, \"absolute\", \"top-0\", \"right-0\", \"w-32\", \"h-32\", \"bg-white/10\", \"rounded-full\", \"blur-3xl\", \"transform\", \"translate-x-16\", \"-translate-y-16\"], [1, \"absolute\", \"bottom-0\", \"left-0\", \"w-40\", \"h-40\", \"bg-white/10\", \"rounded-full\", \"blur-3xl\", \"transform\", \"-translate-x-20\", \"translate-y-20\"], [1, \"absolute\", \"inset-0\", \"opacity-10\"], [1, \"grid\", \"grid-cols-12\", \"h-full\"], [1, \"border-r\", \"border-white/20\"], [1, \"grid\", \"grid-rows-12\", \"w-full\", \"absolute\", \"top-0\", \"left-0\", \"h-full\"], [1, \"border-b\", \"border-white/20\"], [1, \"text-center\", \"text-white\", \"relative\", \"z-10\"], [1, \"text-3xl\", \"font-bold\", \"mb-4\", \"text-white\"], [1, \"text-white/80\"], [1, \"mt-8\", \"relative\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-24\", \"w-24\", \"mx-auto\", \"text-white\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"1.5\", \"d\", \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"1.5\", \"d\", \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"], [1, \"absolute\", \"inset-0\", \"bg-white/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"md:w-1/2\", \"p-8\", \"md:p-12\", \"relative\"], [1, \"absolute\", \"top-0\", \"right-0\", \"w-20\", \"h-20\", \"bg-[#4f5fad]/5\", \"dark:bg-[#6d78c9]/5\", \"rounded-full\", \"blur-2xl\"], [1, \"absolute\", \"bottom-0\", \"left-0\", \"w-24\", \"h-24\", \"bg-[#4f5fad]/5\", \"dark:bg-[#6d78c9]/5\", \"rounded-full\", \"blur-2xl\"], [1, \"text-center\", \"mb-8\", \"relative\"], [1, \"text-2xl\", \"font-bold\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\"], [1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mt-2\"], [1, \"space-y-6\", \"relative\", \"z-10\", 3, \"ngSubmit\"], [\"f\", \"ngForm\"], [1, \"group\"], [\"for\", \"email\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mb-1\", \"transition-colors\"], [1, \"relative\"], [\"id\", \"email\", \"type\", \"email\", \"name\", \"email\", \"ngModel\", \"\", \"required\", \"\", \"email\", \"\", \"placeholder\", \"<EMAIL>\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\"], [\"email\", \"ngModel\"], [1, \"absolute\", \"inset-y-0\", \"left-0\", \"pl-3\", \"flex\", \"items-center\", \"pointer-events-none\", \"opacity-0\", \"group-focus-within:opacity-100\", \"transition-opacity\"], [1, \"w-0.5\", \"h-4\", \"bg-gradient-to-b\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"rounded-full\"], [\"class\", \"text-[#ff6b69] dark:text-[#ff8785] text-xs mt-1 flex items-center\", 4, \"ngIf\"], [\"for\", \"password\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mb-1\", \"transition-colors\"], [\"id\", \"password\", \"type\", \"password\", \"name\", \"password\", \"ngModel\", \"\", \"required\", \"\", \"placeholder\", \"\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\"], [\"password\", \"ngModel\"], [\"class\", \"bg-[#ff6b69]/10 dark:bg-[#ff6b69]/5 border border-[#ff6b69] dark:border-[#ff6b69]/30 text-[#ff6b69] dark:text-[#ff8785] p-3 rounded-lg text-sm flex items-start\", 4, \"ngIf\"], [\"type\", \"submit\", 1, \"w-full\", \"relative\", \"overflow-hidden\", \"group\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#3d4a85]\", \"dark:to-[#6d78c9]\", \"rounded-lg\", \"transition-transform\", \"duration-300\", \"group-hover:scale-105\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#3d4a85]\", \"dark:to-[#6d78c9]\", \"rounded-lg\", \"opacity-0\", \"group-hover:opacity-100\", \"blur-xl\", \"transition-opacity\", \"duration-300\"], [1, \"relative\", \"block\", \"text-white\", \"font-bold\", \"py-3\", \"px-4\", \"rounded-lg\", \"transition-all\"], [1, \"fixed\", \"top-4\", \"right-4\", \"left-4\", \"md:left-auto\", \"md:w-96\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"border-l-4\", \"border-[#4f5fad]\", \"dark:border-[#6d78c9]\", \"rounded-lg\", \"p-4\", \"shadow-md\", \"dark:shadow-[0_4px_20px_rgba(0,0,0,0.3)]\", \"z-50\", \"backdrop-blur-sm\"], [1, \"flex\", \"items-center\"], [1, \"fas\", \"fa-info-circle\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"text-lg\", \"mr-3\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/20\", \"dark:bg-[#6d78c9]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"text-sm\", \"font-medium\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [1, \"text-[#ff6b69]\", \"dark:text-[#ff8785]\", \"text-xs\", \"mt-1\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-exclamation-circle\", \"mr-1\"], [1, \"bg-[#ff6b69]/10\", \"dark:bg-[#ff6b69]/5\", \"border\", \"border-[#ff6b69]\", \"dark:border-[#ff6b69]/30\", \"text-[#ff6b69]\", \"dark:text-[#ff8785]\", \"p-3\", \"rounded-lg\", \"text-sm\", \"flex\", \"items-start\"], [1, \"fas\", \"fa-exclamation-triangle\", \"mt-0.5\", \"mr-2\"]],\n        template: function AuthAdminLayoutComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r7 = i0.ɵɵgetCurrentView();\n            i0.ɵɵtemplate(0, AuthAdminLayoutComponent_div_0_Template, 7, 1, \"div\", 0);\n            i0.ɵɵelementStart(1, \"div\", 1);\n            i0.ɵɵpipe(2, \"async\");\n            i0.ɵɵelement(3, \"div\", 2);\n            i0.ɵɵelementStart(4, \"div\", 3);\n            i0.ɵɵelement(5, \"div\", 4)(6, \"div\", 5);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(7, \"div\", 6)(8, \"div\", 7)(9, \"div\", 8);\n            i0.ɵɵelement(10, \"div\", 9)(11, \"div\", 10);\n            i0.ɵɵelementStart(12, \"div\", 11)(13, \"div\", 12);\n            i0.ɵɵelement(14, \"div\", 13)(15, \"div\", 13)(16, \"div\", 13)(17, \"div\", 13)(18, \"div\", 13)(19, \"div\", 13)(20, \"div\", 13)(21, \"div\", 13)(22, \"div\", 13)(23, \"div\", 13)(24, \"div\", 13);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(25, \"div\", 14);\n            i0.ɵɵelement(26, \"div\", 15)(27, \"div\", 15)(28, \"div\", 15)(29, \"div\", 15)(30, \"div\", 15)(31, \"div\", 15)(32, \"div\", 15)(33, \"div\", 15)(34, \"div\", 15)(35, \"div\", 15)(36, \"div\", 15);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(37, \"div\", 16)(38, \"h2\", 17);\n            i0.ɵɵtext(39, \" Espace Administrateur \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(40, \"p\", 18);\n            i0.ɵɵtext(41, \"Gestion compl\\u00E8te de votre plateforme\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(42, \"div\", 19);\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(43, \"svg\", 20);\n            i0.ɵɵelement(44, \"path\", 21)(45, \"path\", 22);\n            i0.ɵɵelementEnd();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelement(46, \"div\", 23);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(47, \"div\", 24);\n            i0.ɵɵelement(48, \"div\", 25)(49, \"div\", 26);\n            i0.ɵɵelementStart(50, \"div\", 27)(51, \"h1\", 28);\n            i0.ɵɵtext(52, \" Connexion Admin \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(53, \"p\", 29);\n            i0.ɵɵtext(54, \" Acc\\u00E9dez \\u00E0 votre tableau de bord \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(55, \"form\", 30, 31);\n            i0.ɵɵlistener(\"ngSubmit\", function AuthAdminLayoutComponent_Template_form_ngSubmit_55_listener() {\n              i0.ɵɵrestoreView(_r7);\n              const _r1 = i0.ɵɵreference(56);\n              return i0.ɵɵresetView(ctx.loginAdmin(_r1));\n            });\n            i0.ɵɵelementStart(57, \"div\", 32)(58, \"label\", 33);\n            i0.ɵɵtext(59, \"Email\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(60, \"div\", 34);\n            i0.ɵɵelement(61, \"input\", 35, 36);\n            i0.ɵɵelementStart(63, \"div\", 37);\n            i0.ɵɵelement(64, \"div\", 38);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(65, AuthAdminLayoutComponent_div_65_Template, 3, 1, \"div\", 39);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(66, \"div\", 32)(67, \"label\", 40);\n            i0.ɵɵtext(68, \"Mot de passe\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(69, \"div\", 34);\n            i0.ɵɵelement(70, \"input\", 41, 42);\n            i0.ɵɵelementStart(72, \"div\", 37);\n            i0.ɵɵelement(73, \"div\", 38);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(74, AuthAdminLayoutComponent_div_74_Template, 3, 0, \"div\", 39);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(75, AuthAdminLayoutComponent_div_75_Template, 4, 1, \"div\", 43);\n            i0.ɵɵelementStart(76, \"button\", 44);\n            i0.ɵɵelement(77, \"div\", 45)(78, \"div\", 46);\n            i0.ɵɵelementStart(79, \"span\", 47);\n            i0.ɵɵtext(80, \" Se connecter \");\n            i0.ɵɵelementEnd()()()()()()();\n          }\n          if (rf & 2) {\n            const _r2 = i0.ɵɵreference(62);\n            const _r4 = i0.ɵɵreference(71);\n            i0.ɵɵproperty(\"ngIf\", ctx.messageFromRedirect);\n            i0.ɵɵadvance(1);\n            i0.ɵɵclassProp(\"dark\", i0.ɵɵpipeBind1(2, 6, ctx.isDarkMode$));\n            i0.ɵɵadvance(64);\n            i0.ɵɵproperty(\"ngIf\", _r2.invalid && (_r2.dirty || _r2.touched));\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"ngIf\", _r4.invalid && (_r4.dirty || _r4.touched));\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.messageAuthError);\n          }\n        },\n        dependencies: [i6.NgIf, i7.ɵNgNoValidate, i7.DefaultValueAccessor, i7.NgControlStatus, i7.NgControlStatusGroup, i7.RequiredValidator, i7.EmailValidator, i7.NgModel, i7.NgForm, i6.AsyncPipe],\n        styles: [\"@charset \\\"UTF-8\\\";.notification[_ngcontent-%COMP%]{position:fixed;top:20px;right:20px;padding:15px 25px;border-radius:8px;box-shadow:0 4px 12px #00000026;z-index:1000;display:flex;align-items:center;max-width:350px;animation:_ngcontent-%COMP%_slideIn .3s ease-out forwards;font-family:Segoe UI,Roboto,sans-serif;font-size:14px;line-height:1.5}.notification-info[_ngcontent-%COMP%]{background-color:#e6f7ff;color:#0052cc;border-left:4px solid #1890ff}.notification-error[_ngcontent-%COMP%]{background-color:#fff1f0;color:#cf1322;border-left:4px solid #ff4d4f}.notification-success[_ngcontent-%COMP%]{background-color:#f6ffed;color:#389e0d;border-left:4px solid #52c41a}.notification-icon[_ngcontent-%COMP%]{margin-right:12px;font-size:18px}@keyframes _ngcontent-%COMP%_slideIn{0%{transform:translate(100%);opacity:0}to{transform:translate(0);opacity:1}}@keyframes _ngcontent-%COMP%_fadeOut{to{opacity:0;transform:translateY(-20px)}}.notification-auto-hide[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeOut .5s ease-in 4.5s forwards}\"]\n      });\n    }\n  }\n  return AuthAdminLayoutComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}